import time
import json
import os
from datetime import datetime, timedelta
from futu import *
from rsi_strategies import rsi_info_display


def load_config():
    """加载配置文件"""
    config_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'config.json')
    try:
        with open(config_path, 'r') as f:
            return json.load(f)
    except Exception as e:
        print(f"无法加载配置文件: {str(e)}")
        return {
            "server": "127.0.0.1",
            "port": 11111,
            "monitor": {
                "default_group_names": ["chicang"],
                "kline_num": 1000,
                "refresh_interval": 300,
                "trading_hours": {
                    "morning_start": "09:30",
                    "morning_end": "12:00",
                    "afternoon_start": "13:00",
                    "afternoon_end": "16:00"
                }
            }
        }


# 加载配置
CONFIG = load_config()


class StockQuoteMonitor:
    def __init__(self, host=None, port=None):
        # 使用配置文件中的值，如果提供了参数则覆盖
        self.host = host if host is not None else CONFIG["server"]
        self.port = port if port is not None else CONFIG["port"]
        self.quote_ctx = OpenQuoteContext(host=self.host, port=self.port)
        self.symbollist = []
        self.trading_hours = CONFIG["monitor"]["trading_hours"]
        
        print(f"已连接到富途服务器: {self.host}:{self.port}")

    def is_trading_time(self):
        """
        判断是否在港股交易时间
        交易时间：
        上午9:30 - 12:00
        下午13:00 - 16:00
        排除周末和法定节假日
        """
        now = datetime.now()

        # 判断是否为工作日（周一到周五）
        if now.weekday() >= 5:
            return False

        # 获取当前时间
        current_time = now.time()

        # 从配置文件获取交易时段
        morning_start = datetime.strptime(self.trading_hours["morning_start"], '%H:%M').time()
        morning_end = datetime.strptime(self.trading_hours["morning_end"], '%H:%M').time()
        afternoon_start = datetime.strptime(self.trading_hours["afternoon_start"], '%H:%M').time()
        afternoon_end = datetime.strptime(self.trading_hours["afternoon_end"], '%H:%M').time()

        return (morning_start <= current_time <= morning_end) or \
            (afternoon_start <= current_time <= afternoon_end)

    def get_user_security_by_groupname(self, groupname):
        """获取用户自选股列表"""
        ret, data = self.quote_ctx.get_user_security(groupname)

        if ret == RET_OK and data.shape[0] > 0:
            print(f"获取 {groupname} 分组的股票列表")
            return data['code'].values.tolist()
        else:
            print(f'获取 {groupname} 分组股票失败:', data)
            return []

    def init_symbol_list(self, groupnames=None):
        """初始化股票列表"""
        if groupnames is None:
            groupnames = CONFIG["monitor"]["default_group_names"]
            
        self.symbollist = []
        for groupname in groupnames:
            symbols = self.get_user_security_by_groupname(groupname)
            self.symbollist.extend(symbols)
        
        print(f"已初始化 {len(self.symbollist)} 只股票")

    def monitor_stocks(self, knum=None, interval=None):
        """监控股票行情"""
        # 使用配置文件中的值，如果未提供则使用默认值
        knum = knum if knum is not None else CONFIG["monitor"]["kline_num"]
        interval = interval if interval is not None else CONFIG["monitor"]["refresh_interval"]
        
        print(f"开始监控股票，K线数量: {knum}，刷新间隔: {interval}秒")
        
        # 订阅K线数据
        ret_sub, err_message = self.quote_ctx.subscribe(
            self.symbollist,
            [SubType.K_15M],
            subscribe_push=True
        )

        if ret_sub != RET_OK:
            print('订阅失败:', err_message)
            return

        try:
            while True:
                # 检查是否在交易时间
                if not self.is_trading_time():
                    print("非交易时间，等待下一个交易时段...")
                    self._wait_for_next_trading_time()
                    continue

                for code in self.symbollist:
                    ret, data = self.quote_ctx.get_cur_kline(
                        code, knum, KLType.K_15M, AuType.NONE
                    )

                    if ret == RET_OK:
                        print(
                            code,
                            data.name.iloc[-1],
                            data.close.iloc[-1],
                            data.time_key.iloc[-1]
                        )
                        rsi_info_display(data, code, data.name.iloc[-1], '15m')
                    else:
                        print('获取K线数据错误:', data)

                    time.sleep(0.0011)  # 控制请求频率

                print(time.asctime(), f'休息{interval / 60}分钟')
                time.sleep(interval)

        except KeyboardInterrupt:
            print("程序被手动中止")
        finally:
            self.quote_ctx.close()

    def _wait_for_next_trading_time(self):
        """等待下一个交易时段"""
        now = datetime.now()

        # 从配置文件获取交易时段
        morning_start_time = self.trading_hours["morning_start"]
        morning_end_time = self.trading_hours["morning_end"]
        afternoon_start_time = self.trading_hours["afternoon_start"]
        afternoon_end_time = self.trading_hours["afternoon_end"]

        # 判断当前时间并计算下一个交易时段
        if now.time() < datetime.strptime(morning_start_time, '%H:%M').time():
            # 早上开盘前
            next_time = now.replace(
                hour=int(morning_start_time.split(':')[0]), 
                minute=int(morning_start_time.split(':')[1]), 
                second=0, 
                microsecond=0
            )
        elif now.time() < datetime.strptime(morning_end_time, '%H:%M').time():
            # 上午交易时段
            next_time = now
        elif now.time() < datetime.strptime(afternoon_start_time, '%H:%M').time():
            # 中午休市期间
            next_time = now.replace(
                hour=int(afternoon_start_time.split(':')[0]), 
                minute=int(afternoon_start_time.split(':')[1]), 
                second=0, 
                microsecond=0
            )
        elif now.time() < datetime.strptime(afternoon_end_time, '%H:%M').time():
            # 下午交易时段
            next_time = now
        else:
            # 收盘后，等到第二个交易日早上开盘
            next_time = (now + timedelta(days=1)).replace(
                hour=int(morning_start_time.split(':')[0]), 
                minute=int(morning_start_time.split(':')[1]), 
                second=0, 
                microsecond=0
            )

        wait_seconds = (next_time - now).total_seconds()
        print(f"将在 {next_time} 进行下一次监控")
        time.sleep(wait_seconds)


def main():
    monitor = StockQuoteMonitor()
    monitor.init_symbol_list()
    monitor.monitor_stocks()


if __name__ == "__main__":
    main()