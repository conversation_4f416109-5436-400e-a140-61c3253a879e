import time
from futu import *
import pandas as pd

# 定义股票符号和订阅的K线数据类型
stocks = ['HK.07226', 'HK.01772', 'HK.00358', 'HK.00700']
context = {symbol: {'prices': [], 'positions': 0} for symbol in stocks}

# 创建行情对象
quote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)
trade_ctx = OpenHKTradeContext(host='127.0.0.1', port=11111)  # 假设交易在香港市场

# 订阅股票15分钟K线数据
for stock in stocks:
    ret, data = quote_ctx.subscribe(stock, [SubType.K_15M], subscribe_push=True)
    if ret != RET_OK:
        print(f'subscribe {stock} error:', data)


# 处理接收到的K线数据
class MyKlineHandler(StockQuoteHandlerBase):
    def on_recv_rsp(self, rsp_pb):
        ret_code, data = super(MyKline<PERSON><PERSON><PERSON>, self).on_recv_rsp(rsp_pb)
        if ret_code != RET_OK:
            print(f"Error: {data}")
            return RET_ERROR, data

        for stock_code in data['code'].unique():
            df_stock = data[data['code'] == stock_code]
            context[stock_code]['prices'] += df_stock['close'].tolist()

            # 打印接收到的K线数据
            for index, row in df_stock.iterrows():
                print(
                    f"Symbol: {row['code']}, Time: {row['time_key']}, Open: {row['open']}, High: {row['high']}, Low: {row['low']}, Close: {row['close']}, Volume: {row['volume']}")

            prices = pd.Series(context[stock_code]['prices'])
            if len(prices) < 13:
                continue  # 少于13个数据点时不计算

            # 计算13周期移动平均值
            sma_13 = prices.rolling(window=13).mean().iloc[-1]

            # 获取当前持仓
            positions = context[stock_code]['positions']

            # 买入和卖出条件判断
            close_price = prices.iloc[-1]
            if close_price > sma_13 and positions <= 0:  # Cross up
                ret, response = trade_ctx.place_order(price=close_price, qty=1, code=stock_code, trd_side=TrdSide.BUY)
                if ret == RET_OK:
                    context[stock_code]['positions'] += 1
                    print(f"Bought 1 unit of {stock_code} at {close_price}")
                else:
                    print(f"Buy order failed: {response}")
            elif close_price < sma_13 and positions > 0:  # Cross down
                ret, response = trade_ctx.place_order(price=close_price, qty=1, code=stock_code, trd_side=TrdSide.SELL)
                if ret == RET_OK:
                    context[stock_code]['positions'] -= 1
                    print(f"Sold 1 unit of {stock_code} at {close_price}")
                else:
                    print(f"Sell order failed: {response}")

        return RET_OK, data


# 创建并设置K线处理器 (使用 StockQuoteHandlerBase 作为基类)
kline_handler = MyKlineHandler()
quote_ctx.set_handler(kline_handler)

# 保持订阅和接收数据运行
try:
    while True:
        print(time.asctime())
        time.sleep(60)  # 每60秒检查一次
except KeyboardInterrupt:
    quote_ctx.close()  # 用户手动终止时关闭连接
    trade_ctx.close()
