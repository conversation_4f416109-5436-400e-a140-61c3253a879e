from futu import *

# 创建行情上下文
quote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)

# 获取股票基本信息
ret, data = quote_ctx.get_stock_basicinfo(Market.HK, SecurityType.STOCK, ['HK.00700'])

if ret == RET_OK:
    lot_size = data['lot_size'][0]  # 获取最小交易单位
    print(f"最小交易单位: {lot_size}")
else:
    print(f"获取基本信息失败: {data}")

# 关闭上下文
quote_ctx.close()


from futu import *

# 创建行情上下文
quote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)

# 获取股票快照
ret, data = quote_ctx.get_market_snapshot('HK.00700')

if ret == RET_OK:
    lot_size = data['lot_size'][0]  # 获取最小交易单位
    print(f"最小交易单位: {lot_size}")
else:
    print(f"获取快照失败: {data}")

# 关闭上下文
quote_ctx.close()