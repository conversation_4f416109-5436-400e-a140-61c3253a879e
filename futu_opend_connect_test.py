from futu import *
import time


class FutuTest:
    def __init__(self, host, port):
        self.host = host
        self.port = port
        self.quote_ctx = None
        self.trade_ctx = None

    def connect(self):
        """建立行情连接"""
        try:
            self.quote_ctx = OpenQuoteContext(host=self.host, port=self.port)
            print(f"成功连接到服务器 {self.host}:{self.port}")
            return True
        except Exception as e:
            print(f"连接失败: {str(e)}")
            return False

    def test_connection(self):
        """测试连接状态"""
        if not self.quote_ctx:
            print("未建立连接")
            return False

        ret, data = self.quote_ctx.get_global_state()
        if ret == RET_OK:
            print("\n全局状态：")
            print(data)
            return True
        else:
            print(f"获取全局状态失败: {data}")
            return False

    def test_quote(self):
        """测试行情获取"""
        if not self.quote_ctx:
            return False

        # 测试获取港股基本报价
        print("\n测试获取腾讯控股(HK.00700)基本报价：")
        ret, data = self.quote_ctx.get_market_snapshot(['HK.00700'])
        if ret == RET_OK:
            print(data[['code', 'last_price', 'open_price', 'high_price', 'low_price', 'volume']])
        else:
            print(f"获取报价失败: {data}")

        # 测试订阅实时报价
        print("\n测试订阅报价：")
        ret, data = self.quote_ctx.subscribe(['HK.00700'], [SubType.QUOTE])
        if ret == RET_OK:
            print("订阅成功")
            # 等待一下获取实时数据
            time.sleep(2)
            ret, data = self.quote_ctx.get_stock_quote(['HK.00700'])
            if ret == RET_OK:
                print("实时报价：")
                print(data[['code', 'last_price', 'open_price', 'high_price', 'low_price', 'volume']])
        else:
            print(f"订阅失败: {data}")

    def run_all_tests(self):
        """运行所有测试"""
        print("开始测试...")

        # 测试连接
        if not self.connect():
            return

        # 等待连接稳定
        time.sleep(1)

        # 测试连接状态
        if not self.test_connection():
            return

        # 测试行情
        self.test_quote()

    def close(self):
        """关闭连接"""
        if self.quote_ctx:
            self.quote_ctx.close()
            print("已关闭连接")


def main():
    # 创建测试实例
    tester = FutuTest(host='**********', port=11111)

    try:
        # 运行所有测试
        tester.run_all_tests()
    except Exception as e:
        print(f"测试过程中发生错误: {str(e)}")
    finally:
        # 确保正确关闭连接
        tester.close()


if __name__ == "__main__":
    main()