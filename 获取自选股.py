from futu import *
quote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)

# ret, data = quote_ctx.get_user_security("chicang")

ret, data = quote_ctx.get_user_security("全部")
if ret == RET_OK:
    print(data)
    if data.shape[0] > 0:  # 如果自选股列表不为空
        print(data['code'][0])    # 取第一条的股票代码
        print(data['code'].values.tolist())   # 转为 list
else:
    print('error:', data)
quote_ctx.close() # 结束后记得关闭当条连接，防止连接条数用尽
