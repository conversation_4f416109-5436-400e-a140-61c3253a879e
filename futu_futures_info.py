from futu import *
import pandas as pd

HK = "HK"
US = "US"
SH = "SH"
SZ = "SZ"
HK_FUTURE = "HK_FUTURE"
SG = "SG"
JP = "JP"
market_all = [HK, US, SH, SZ, HK_FUTURE, SG, JP]
host='localhost'

def create_quote_context(host=host, port=11111):
    return OpenQuoteContext(host=host, port=port)


def get_futures_list(quote_ctx, market=Market.SG):
    ret_code, ret_data = quote_ctx.get_stock_basicinfo(market, SecurityType.FUTURE)
    if ret_code != RET_OK:
        print(f"获取期货列表时出错: {ret_data}")
        return None
    df = pd.DataFrame(ret_data)
    return df[df['main_contract'] == True][['code', 'name']].values.tolist()


def get_futures_info(quote_ctx, futures_list):
    all_data = []
    chunk_size = 199  # API限制
    for i in range(0, len(futures_list), chunk_size):
        chunk = futures_list[i:i + chunk_size]
        chunk_codes = [item[0] for item in chunk]
        ret, data = quote_ctx.get_future_info(chunk_codes)
        if ret == RET_OK:
            # 将名称添加到数据中
            data['name'] = data['code'].map(dict(futures_list))
            all_data.append(data)
        else:
            print(f"获取第 {i // chunk_size + 1} 批期货信息时出错: {data}")
    return pd.concat(all_data) if all_data else None


def print_futures_info(futures_info):
    if futures_info is not None:
        for index, row in futures_info[['code', 'name', 'trade_time']].iterrows():
            print(f"{index + 1}. 代码: {row['code']}, 名称: {row['name']}, 交易时间: {row['trade_time']}")


def main(market):
    quote_ctx = create_quote_context()
    try:
        for Market in market:
            futures_list = get_futures_list(quote_ctx, Market)
            if futures_list:
                print(f"总共获取到 {len(futures_list)} 个期货合约")
                futures_info = get_futures_info(quote_ctx, futures_list)
                print_futures_info(futures_info)
    finally:
        quote_ctx.close()


if __name__ == "__main__":
    main([HK])
