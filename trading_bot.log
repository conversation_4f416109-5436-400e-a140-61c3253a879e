2024-10-07 18:25:56,538 - TradingBot - INFO - Connection established successfully
2024-10-07 18:25:56,538 - TradingBot - INFO - Starting the trading bot...
2024-10-07 18:25:56,545 - TradingBot - INFO - Subscribed successfully: None
2024-10-07 18:25:56,596 - TradingBot - INFO - TickerUpdate:        code  name  ...     type  push_data_type
0  HK.00700  ��Ѷ�ع�  ...  AUCTION           CACHE

[1 rows x 10 columns]
2024-10-07 18:25:56,598 - TradingBot - INFO - Strategy executed for HK.00700
2024-10-07 18:25:56,836 - TradingBot - INFO - OrderBookUpdate: {'code': 'HK.00700', 'name': '��Ѷ�ع�', 'svr_recv_time_bid': '', 'svr_recv_time_ask': '', 'Bid': [(478.2, 700, 5, {}), (478.0, 42600, 23, {}), (477.8, 3500, 14, {}), (477.6, 5200, 11, {}), (477.4, 23800, 12, {}), (477.2, 83200, 12, {}), (477.0, 66900, 32, {}), (476.8, 32500, 7, {}), (476.6, 3800, 4, {}), (476.4, 33400, 6, {})], 'Ask': [(478.4, 56400, 9, {}), (478.6, 22400, 8, {}), (478.8, 34200, 8, {}), (479.0, 44900, 49, {}), (479.2, 21400, 14, {}), (479.4, 34000, 15, {}), (479.6, 50800, 17, {}), (479.8, 71600, 24, {}), (480.0, 198600, 400, {}), (480.2, 52900, 24, {})]}
2024-10-07 18:25:56,838 - TradingBot - WARNING - Received data without price information
2024-10-07 18:27:40,698 - TradingBot - INFO - Received shutdown signal. Initiating graceful shutdown...
2024-10-07 18:27:40,698 - TradingBot - INFO - Shutting down the trading bot...
2024-10-07 18:27:40,700 - TradingBot - INFO - Trading bot shut down successfully.
