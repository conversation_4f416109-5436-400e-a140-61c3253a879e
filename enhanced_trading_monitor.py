"""
增强版交易监控程序
集成统一交易时间管理组件的示例
支持多交易所时间控制
"""

from futu import *
import time
from datetime import datetime, timedelta
from trading_time_manager import TradingTimeManager, Exchange
import json
import os


class EnhancedTradingMonitor:
    """增强版交易监控器"""
    
    def __init__(self, config_file="enhanced_trading_config.json"):
        """
        初始化监控器
        
        Args:
            config_file: 配置文件路径
        """
        self.config = self._load_config(config_file)
        self.time_manager = TradingTimeManager()
        
        # 初始化富途连接
        self.quote_ctx = None
        self.trade_ctx = None
        self._init_futu_connection()
        
        # 监控状态
        self.is_running = False
        self.last_check_time = {}
        
    def _load_config(self, config_file):
        """加载配置文件"""
        default_config = {
            "futu": {
                "host": "127.0.0.1",
                "port": 11111
            },
            "monitoring": {
                "refresh_interval": 60,  # 秒
                "kline_num": 1000,
                "exchanges": ["HK", "SSE", "SZSE"],  # 监控的交易所
                "symbols": {
                    "HK": ["HK.00700", "HK.00981"],  # 港股代码
                    "SSE": ["SH.600519", "SH.000001"],  # 上证代码
                    "SZSE": ["SZ.000002", "SZ.300059"]  # 深证代码
                }
            },
            "alerts": {
                "enabled": True,
                "methods": ["console", "file"]  # 可选: console, file, email, wechat
            }
        }
        
        if os.path.exists(config_file):
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    user_config = json.load(f)
                    # 合并配置
                    default_config.update(user_config)
            except Exception as e:
                print(f"加载配置文件失败: {e}，使用默认配置")
        
        return default_config
    
    def _init_futu_connection(self):
        """初始化富途连接"""
        try:
            futu_config = self.config["futu"]
            self.quote_ctx = OpenQuoteContext(
                host=futu_config["host"], 
                port=futu_config["port"]
            )
            print(f"已连接到富途服务器: {futu_config['host']}:{futu_config['port']}")
        except Exception as e:
            print(f"连接富途服务器失败: {e}")
    
    def is_exchange_trading(self, exchange_code):
        """检查指定交易所是否在交易时间"""
        exchange_map = {
            "HK": Exchange.HK,
            "SSE": Exchange.SSE,
            "SZSE": Exchange.SZSE,
            "SHFE": Exchange.SHFE,
            "DCE": Exchange.DCE,
            "CZCE": Exchange.CZCE,
            "CFFEX": Exchange.CFFEX,
            "GFEX": Exchange.GFEX
        }
        
        exchange = exchange_map.get(exchange_code)
        if exchange:
            return self.time_manager.is_trading_time(exchange)
        return False
    
    def get_trading_status_summary(self):
        """获取所有监控交易所的交易状态摘要"""
        monitored_exchanges = self.config["monitoring"]["exchanges"]
        status = {}
        
        for exchange_code in monitored_exchanges:
            is_trading = self.is_exchange_trading(exchange_code)
            exchange_map = {
                "HK": "港股", "SSE": "上证", "SZSE": "深证",
                "SHFE": "上期所", "DCE": "大商所", "CZCE": "郑商所",
                "CFFEX": "中金所", "GFEX": "广期所"
            }
            
            status[exchange_code] = {
                "name": exchange_map.get(exchange_code, exchange_code),
                "is_trading": is_trading,
                "status_text": "🟢 交易中" if is_trading else "🔴 休市"
            }
        
        return status
    
    def monitor_symbols(self, exchange_code, symbols):
        """监控指定交易所的股票"""
        if not self.is_exchange_trading(exchange_code):
            return []
        
        results = []
        kline_num = self.config["monitoring"]["kline_num"]
        
        for symbol in symbols:
            try:
                if self.quote_ctx:
                    ret, data = self.quote_ctx.get_cur_kline(
                        symbol, kline_num, KLType.K_15M, AuType.NONE
                    )
                    
                    if ret == RET_OK and not data.empty:
                        latest_data = {
                            "symbol": symbol,
                            "name": data.name.iloc[-1] if 'name' in data.columns else symbol,
                            "price": data.close.iloc[-1],
                            "time": data.time_key.iloc[-1],
                            "exchange": exchange_code
                        }
                        results.append(latest_data)
                        
                        # 这里可以添加技术指标计算和信号判断
                        self._analyze_symbol(latest_data, data)
                    else:
                        print(f"获取 {symbol} 数据失败: {data}")
                        
            except Exception as e:
                print(f"监控 {symbol} 时出错: {e}")
            
            time.sleep(0.01)  # 避免请求过于频繁
        
        return results
    
    def _analyze_symbol(self, symbol_info, kline_data):
        """分析股票数据并生成信号"""
        # 这里可以集成各种技术分析策略
        # 例如：RSI、MACD、波浪理论等
        
        symbol = symbol_info["symbol"]
        price = symbol_info["price"]
        
        # 简单的价格变化检测示例
        if len(kline_data) >= 2:
            prev_price = kline_data.close.iloc[-2]
            change_pct = (price - prev_price) / prev_price * 100
            
            if abs(change_pct) > 2:  # 价格变化超过2%
                signal = {
                    "symbol": symbol,
                    "name": symbol_info["name"],
                    "type": "price_alert",
                    "message": f"价格变化 {change_pct:.2f}%",
                    "price": price,
                    "prev_price": prev_price,
                    "time": datetime.now(),
                    "exchange": symbol_info["exchange"]
                }
                self._send_alert(signal)
    
    def _send_alert(self, signal):
        """发送交易信号/警报"""
        if not self.config["alerts"]["enabled"]:
            return
        
        alert_methods = self.config["alerts"]["methods"]
        message = (f"【{signal['exchange']}】{signal['name']} ({signal['symbol']}) "
                  f"{signal['message']} 当前价格: {signal['price']}")
        
        if "console" in alert_methods:
            print(f"🚨 交易信号: {message}")
        
        if "file" in alert_methods:
            self._log_to_file(signal, message)
    
    def _log_to_file(self, signal, message):
        """记录信号到文件"""
        log_file = f"trading_signals_{datetime.now().strftime('%Y%m%d')}.log"
        try:
            with open(log_file, 'a', encoding='utf-8') as f:
                timestamp = signal['time'].strftime('%Y-%m-%d %H:%M:%S')
                f.write(f"[{timestamp}] {message}\n")
        except Exception as e:
            print(f"写入日志文件失败: {e}")
    
    def run_monitoring_cycle(self):
        """运行一次监控周期"""
        print(f"\n=== 监控周期开始 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')} ===")
        
        # 显示交易状态摘要
        status_summary = self.get_trading_status_summary()
        print("交易所状态:")
        for code, info in status_summary.items():
            print(f"  {info['name']}: {info['status_text']}")
        
        # 监控各交易所的股票
        monitoring_config = self.config["monitoring"]
        total_results = []
        
        for exchange_code in monitoring_config["exchanges"]:
            if self.is_exchange_trading(exchange_code):
                symbols = monitoring_config["symbols"].get(exchange_code, [])
                if symbols:
                    print(f"\n监控 {status_summary[exchange_code]['name']} 股票:")
                    results = self.monitor_symbols(exchange_code, symbols)
                    total_results.extend(results)
                    
                    for result in results:
                        print(f"  {result['name']} ({result['symbol']}): {result['price']} @ {result['time']}")
            else:
                print(f"\n{status_summary[exchange_code]['name']} 非交易时间，跳过监控")
        
        print(f"=== 监控周期结束，共监控 {len(total_results)} 只股票 ===")
        return total_results
    
    def start_monitoring(self):
        """开始监控"""
        print("🚀 启动增强版交易监控器")
        print(f"监控交易所: {', '.join(self.config['monitoring']['exchanges'])}")
        print(f"刷新间隔: {self.config['monitoring']['refresh_interval']} 秒")
        
        self.is_running = True
        refresh_interval = self.config["monitoring"]["refresh_interval"]
        
        try:
            while self.is_running:
                self.run_monitoring_cycle()
                
                # 检查是否有任何交易所在交易
                any_trading = any(
                    self.is_exchange_trading(code) 
                    for code in self.config["monitoring"]["exchanges"]
                )
                
                if any_trading:
                    print(f"等待 {refresh_interval} 秒后进行下一次监控...")
                    time.sleep(refresh_interval)
                else:
                    print("所有监控的交易所都已休市，等待60秒后重新检查...")
                    time.sleep(60)
                    
        except KeyboardInterrupt:
            print("\n收到停止信号，正在关闭监控器...")
            self.stop_monitoring()
    
    def stop_monitoring(self):
        """停止监控"""
        self.is_running = False
        if self.quote_ctx:
            self.quote_ctx.close()
        print("✅ 监控器已停止")
    
    def get_next_trading_times(self):
        """获取各交易所的下一个交易时间"""
        exchange_map = {
            "HK": Exchange.HK, "SSE": Exchange.SSE, "SZSE": Exchange.SZSE,
            "SHFE": Exchange.SHFE, "DCE": Exchange.DCE, "CZCE": Exchange.CZCE,
            "CFFEX": Exchange.CFFEX, "GFEX": Exchange.GFEX
        }
        
        next_times = {}
        for code in self.config["monitoring"]["exchanges"]:
            exchange = exchange_map.get(code)
            if exchange:
                next_time = self.time_manager.get_next_trading_time(exchange)
                next_times[code] = next_time
        
        return next_times


def create_sample_config():
    """创建示例配置文件"""
    config = {
        "futu": {
            "host": "127.0.0.1",
            "port": 11111
        },
        "monitoring": {
            "refresh_interval": 300,
            "kline_num": 1000,
            "exchanges": ["HK", "SSE"],
            "symbols": {
                "HK": ["HK.00700", "HK.00981", "HK.01810"],
                "SSE": ["SH.600519", "SH.600036"],
                "SZSE": ["SZ.000002", "SZ.300059"]
            }
        },
        "alerts": {
            "enabled": True,
            "methods": ["console", "file"]
        }
    }
    
    with open("enhanced_trading_config.json", 'w', encoding='utf-8') as f:
        json.dump(config, f, ensure_ascii=False, indent=2)
    
    print("已创建示例配置文件: enhanced_trading_config.json")


if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "--create-config":
        create_sample_config()
        sys.exit(0)
    
    # 创建并启动监控器
    monitor = EnhancedTradingMonitor()
    
    try:
        monitor.start_monitoring()
    except Exception as e:
        print(f"监控器运行出错: {e}")
    finally:
        monitor.stop_monitoring()
