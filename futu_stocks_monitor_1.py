import sys
import time
import argparse
from datetime import datetime, timedelta
from futu import *
from rsi_strategies import rsi_info_display


class StockQuoteMonitor:
    def __init__(self):
        self.quote_ctx = None
        self.symbollist = []

    def connect(self, host='127.0.0.1', port=11111):
        """建立与 FutuOpenD 的连接"""
        if self.quote_ctx is None:
            self.quote_ctx = OpenQuoteContext(host=host, port=port)
            print("已连接到 FutuOpenD")

    def disconnect(self):
        """断开与 FutuOpenD 的连接"""
        if self.quote_ctx:
            self.quote_ctx.close()
            self.quote_ctx = None
            print("已断开与 FutuOpenD 的连接")

    def is_trading_time(self):
        """判断是否在港股交易时间"""
        now = datetime.now()

        if now.weekday() >= 5:
            return False

        current_time = now.time()
        morning_start = datetime.strptime('09:30', '%H:%M').time()
        morning_end = datetime.strptime('12:00', '%H:%M').time()
        afternoon_start = datetime.strptime('13:00', '%H:%M').time()
        afternoon_end = datetime.strptime('16:00', '%H:%M').time()

        return (morning_start <= current_time <= morning_end) or \
            (afternoon_start <= current_time <= afternoon_end)

    def get_user_security_by_groupname(self, groupname):
        """获取用户自选股列表"""
        if not self.quote_ctx:
            raise RuntimeError("未连接到 FutuOpenD")

        ret, data = self.quote_ctx.get_user_security(groupname)

        if ret == RET_OK and data.shape[0] > 0:
            print(f"获取 {groupname} 分组的股票列表")
            return data['code'].values.tolist()
        else:
            print(f'获取 {groupname} 分组股票失败: {data}')
            return []

    def init_symbol_list(self, groupnames):
        """初始化股票列表"""
        self.symbollist = []
        for groupname in groupnames:
            symbols = self.get_user_security_by_groupname(groupname)
            self.symbollist.extend(symbols)

        if not self.symbollist:
            raise RuntimeError("没有获取到任何股票代码")

        print(f"共获取到 {len(self.symbollist)} 只股票")

    def get_kline_type(self, period):
        """根据周期字符串返回对应的K线类型"""
        kline_mapping = {
            '1m': KLType.K_1M,
            '5m': KLType.K_5M,
            '15m': KLType.K_15M,
            '30m': KLType.K_30M,
            '60m': KLType.K_60M,
            'day': KLType.K_DAY,
            'week': KLType.K_WEEK,
            'month': KLType.K_MON
        }
        return kline_mapping.get(period.lower(), KLType.K_15M)

    def get_sub_type(self, period):
        """根据周期字符串返回对应的订阅类型"""
        subtype_mapping = {
            '1m': SubType.K_1M,
            '5m': SubType.K_5M,
            '15m': SubType.K_15M,
            '30m': SubType.K_30M,
            '60m': SubType.K_60M,
            'day': SubType.K_DAY,
            'week': SubType.K_WEEK,
            'month': SubType.K_MON
        }
        return subtype_mapping.get(period.lower(), SubType.K_15M)

    def monitor_stocks(self, period='15m', knum=1000, interval=5 * 60):
        """监控股票行情"""
        if not self.quote_ctx:
            raise RuntimeError("未连接到 FutuOpenD")

        kline_type = self.get_kline_type(period)
        sub_type = self.get_sub_type(period)

        ret_sub, err_message = self.quote_ctx.subscribe(
            self.symbollist,
            [sub_type],
            subscribe_push=True
        )

        if ret_sub != RET_OK:
            print('订阅失败:', err_message)
            return

        print(f"\n开始监控 {len(self.symbollist)} 只股票...")
        print(f"K线周期: {period}, 数据量: {knum}, 刷新间隔: {interval}秒")

        try:
            while True:
                if not self.is_trading_time():
                    print("\n非交易时间，等待下一个交易时段...")
                    self._wait_for_next_trading_time()
                    continue

                print(f"\n{time.strftime('%Y-%m-%d %H:%M:%S')} 开始获取数据...")

                for code in self.symbollist:
                    ret, data = self.quote_ctx.get_cur_kline(
                        code, knum, kline_type, AuType.NONE
                    )

                    if ret == RET_OK and not data.empty:
                        print(
                            f"{code} {data.name.iloc[-1]}: "
                            f"当前价格 {data.close.iloc[-1]:.3f}, "
                            f"时间 {data.time_key.iloc[-1]}"
                        )
                        rsi_info_display(data, code, data.name.iloc[-1], period)
                    else:
                        print(f'获取 {code} K线数据错误: {data}')

                    time.sleep(0.0011)  # 控制请求频率

                print(f"\n{time.strftime('%Y-%m-%d %H:%M:%S')} "
                      f"休息 {interval / 60:.1f} 分钟...")
                time.sleep(interval)

        except KeyboardInterrupt:
            print("\n程序被手动中止")
        except Exception as e:
            print(f"\n发生错误: {e}")

    def _wait_for_next_trading_time(self):
        """等待下一个交易时段"""
        now = datetime.now()

        if now.time() < datetime.strptime('09:30', '%H:%M').time():
            next_time = now.replace(hour=9, minute=30, second=0, microsecond=0)
        elif now.time() < datetime.strptime('12:00', '%H:%M').time():
            next_time = now
        elif now.time() < datetime.strptime('13:00', '%H:%M').time():
            next_time = now.replace(hour=13, minute=0, second=0, microsecond=0)
        elif now.time() < datetime.strptime('16:00', '%H:%M').time():
            next_time = now
        else:
            next_time = (now + timedelta(days=1)).replace(
                hour=9, minute=30, second=0, microsecond=0
            )
            # 如果下一天是周末，调整到下周一
            while next_time.weekday() >= 5:
                next_time += timedelta(days=1)

        wait_seconds = (next_time - now).total_seconds()
        print(f"将在 {next_time.strftime('%Y-%m-%d %H:%M:%S')} 进行下一次监控")
        time.sleep(wait_seconds)


def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(
        description='港股行情监控程序',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
    # 使用默认参数
    python stock_monitor.py

    # 指定单个分组和周期
    python stock_monitor.py -g mygroup -p 5m

    # 指定多个分组和其他参数
    python stock_monitor.py -g group1 group2 -p 30m -n 500 -i 600
        """
    )

    parser.add_argument(
        '-g', '--groups',
        nargs='+',
        default=['chicang'],
        help='自选股分组名称，可以指定多个，默认为 chicang'
    )

    parser.add_argument(
        '-p', '--period',
        default='15m',
        choices=['1m', '5m', '15m', '30m', '60m', 'day', 'week', 'month'],
        help='K线周期，默认为15分钟'
    )

    parser.add_argument(
        '-n', '--knum',
        type=int,
        default=1000,
        help='获取的K线数量，默认为1000'
    )

    parser.add_argument(
        '-i', '--interval',
        type=int,
        default=300,
        help='刷新间隔（秒），默认为300秒'
    )

    return parser.parse_args()


def run_monitor(args):
    """实际运行监控的函数"""
    try:
        monitor = StockQuoteMonitor()
        monitor.connect()
        monitor.init_symbol_list(args.groups)
        monitor.monitor_stocks(
            period=args.period,
            knum=args.knum,
            interval=args.interval
        )
    except KeyboardInterrupt:
        print("\n程序被手动中止")
    except Exception as e:
        print(f"\n发生错误: {e}")
    finally:
        if 'monitor' in locals():
            monitor.disconnect()


def main():
    # 解析命令行参数
    args = parse_arguments()

    # 如果不是仅仅查看帮助信息，才实际执行程序
    if not any(arg in sys.argv for arg in ['-h', '--help']):
        run_monitor(args)


if __name__ == "__main__":
    main()