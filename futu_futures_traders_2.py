from futu import *
import logging


class FuturesTrader:
    def __init__(self, host='127.0.0.1', port=11111):
        """
        初始化期货交易类

        :param host: Futu OpenD服务器地址，默认为本地
        :param port: Futu OpenD服务器端口，默认为11111
        """
        # 设置日志
        logging.basicConfig(level=logging.INFO,
                            format='%(asctime)s - %(levelname)s: %(message)s')
        self.logger = logging.getLogger(__name__)

        # 创建交易上下文
        self.trade_ctx = OpenFutureTradeContext(host=host, port=port)
        self.logger.info(f"期货交易上下文已创建：{host}:{port}")

    def unlock_trade(self, password):
        """
        解锁交易

        :param password: 交易解锁密码
        :return: 是否解锁成功
        """
        try:
            ret, data = self.trade_ctx.unlock_trade(password)
            if ret == RET_OK:
                self.logger.info("交易解锁成功")
                return True
            else:
                self.logger.error(f"交易解锁失败: {data}")
                return False
        except Exception as e:
            self.logger.error(f"解锁交易异常: {e}")
            return False

    def open_long_position(self, code, qty, price=None):
        """
        开多仓

        :param code: 期货合约代码
        :param qty: 开仓数量
        :param price: 限价(可选)，不传则为市价
        :return: 交易结果
        """
        try:
            # 获取当前市场价格（如果未指定价格）
            if price is None:
                ret, quotes = self.trade_ctx.get_market_snapshot([code])
                if ret == RET_OK and len(quotes) > 0:
                    price = quotes[0]['last_price']
                else:
                    self.logger.error("无法获取市场价格")
                    return None

            # 下单
            ret, data = self.trade_ctx.place_order(
                code=code,
                qty=qty,
                price=price,
                trd_side=TrdSide.BUY,
                order_type=OrderType.NORMAL
            )

            if ret == RET_OK:
                self.logger.info(f"成功开多仓 {code}, 数量: {qty}, 价格: {price}")
                return data
            else:
                self.logger.error(f"开多仓失败: {data}")
                return None

        except Exception as e:
            self.logger.error(f"开多仓异常: {e}")
            return None

    def open_short_position(self, code, qty, price=None):
        """
        开空仓

        :param code: 期货合约代码
        :param qty: 开仓数量
        :param price: 限价(可选)，不传则为市价
        :return: 交易结果
        """
        try:
            # 获取当前市场价格（如果未指定价格）
            if price is None:
                ret, quotes = self.trade_ctx.get_market_snapshot([code])
                if ret == RET_OK and len(quotes) > 0:
                    price = quotes[0]['last_price']
                else:
                    self.logger.error("无法获取市场价格")
                    return None

            # 下单
            ret, data = self.trade_ctx.place_order(
                code=code,
                qty=qty,
                price=price,
                trd_side=TrdSide.SELL,
                order_type=OrderType.NORMAL
            )

            if ret == RET_OK:
                self.logger.info(f"成功开空仓 {code}, 数量: {qty}, 价格: {price}")
                return data
            else:
                self.logger.error(f"开空仓失败: {data}")
                return None

        except Exception as e:
            self.logger.error(f"开空仓异常: {e}")
            return None

    def close_long_position(self, code, qty, price=None):
        """
        平多仓

        :param code: 期货合约代码
        :param qty: 平仓数量
        :param price: 限价(可选)，不传则为市价
        :return: 交易结果
        """
        try:
            # 获取当前市场价格（如果未指定价格）
            if price is None:
                ret, quotes = self.trade_ctx.get_market_snapshot([code])
                if ret == RET_OK and len(quotes) > 0:
                    price = quotes[0]['last_price']
                else:
                    self.logger.error("无法获取市场价格")
                    return None

            # 下单
            ret, data = self.trade_ctx.place_order(
                code=code,
                qty=qty,
                price=price,
                trd_side=TrdSide.SELL,
                order_type=OrderType.NORMAL
            )

            if ret == RET_OK:
                self.logger.info(f"成功平多仓 {code}, 数量: {qty}, 价格: {price}")
                return data
            else:
                self.logger.error(f"平多仓失败: {data}")
                return None

        except Exception as e:
            self.logger.error(f"平多仓异常: {e}")
            return None

    def close_short_position(self, code, qty, price=None):
        """
        平空仓

        :param code: 期货合约代码
        :param qty: 平仓数量
        :param price: 限价(可选)，不传则为市价
        :return: 交易结果
        """
        try:
            # 获取当前市场价格（如果未指定价格）
            if price is None:
                ret, quotes = self.trade_ctx.get_market_snapshot([code])
                if ret == RET_OK and len(quotes) > 0:
                    price = quotes[0]['last_price']
                else:
                    self.logger.error("无法获取市场价格")
                    return None

            # 下单
            ret, data = self.trade_ctx.place_order(
                code=code,
                qty=qty,
                price=price,
                trd_side=TrdSide.BUY,
                order_type=OrderType.NORMAL
            )

            if ret == RET_OK:
                self.logger.info(f"成功平空仓 {code}, 数量: {qty}, 价格: {price}")
                return data
            else:
                self.logger.error(f"平空仓失败: {data}")
                return None

        except Exception as e:
            self.logger.error(f"平空仓异常: {e}")
            return None

    def __del__(self):
        """
        关闭交易上下文
        """
        if hasattr(self, 'trade_ctx'):
            self.trade_ctx.close()
            self.logger.info("期货交易上下文已关闭")


# 使用示例
def main():
    # 创建期货交易实例
    trader = FuturesTrader()

    # 解锁交易（请替换为实际交易密码）
    trader.unlock_trade('your_trade_password')

    # 期货合约代码（示例）
    futures_code = 'HK.MHI'  # 恒生指数期货

    # 市价开多仓示例
    trader.open_long_position(futures_code, qty=1)

    # 限价开多仓示例
    trader.open_long_position(futures_code, qty=1, price=20000)

    # 市价平多仓示例
    trader.close_long_position(futures_code, qty=1)


if __name__ == "__main__":
    main()