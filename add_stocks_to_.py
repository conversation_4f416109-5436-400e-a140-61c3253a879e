from futu import *

# 连接 FutuOpenD
quote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)

try:
    # 先查看现有分组情况
    ret, data = quote_ctx.get_user_security_group()
    if ret == RET_OK:
        print("现有分组:")
        print(data)

        # 创建分组名称
        group_name = "鹏华优选成长混合A"

        # 检查该分组是否已存在
        group_exists = False
        if ret == RET_OK and 'groupName' in data.columns:
            group_exists = group_name in data['groupName'].values

        # 您提供的股票列表
        stocks = [
            "SH.603160",  # 汇顶科技
            "SH.688235",  # 百济神州
            "SH.600707",  # 彩虹股份
            "HK.02269",  # 药明生物
            "SH.600276",  # 恒瑞医药
            "HK.01801",  # 信达生物
            "SH.688008",  # 澜起科技
            "HK.00013",  # 和黄医药
            "SZ.002185",  # 华天科技
            "HK.00981"  # 中芯国际
        ]
        for stock in stocks:
            ret, data = quote_ctx.modify_user_security(group_name, 'ADD', [stock])
            # modify_user_security(self, group_name, op, code_list):
            if ret == RET_OK:
                print(f"成功添加 {stock} 到分组")
            else:
                print(f"添加 {stock} 失败: {data}")

except Exception as e:
    print(f"发生错误: {e}")

finally:
    # 关闭连接
    quote_ctx.close()