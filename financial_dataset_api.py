#financialdatasets.ai, api keys:
import pandas as pd
apikey='8201f5b0-041f-4c36-a4bb-41ad55c7078a'


import requests

url = "https://api.financialdatasets.ai/financials/income-statements"

querystring = {"ticker":"aapl","period":"quarterly","limit":"1000"}

headers = {"X-API-KEY": apikey}

response = requests.request("GET", url, headers=headers, params=querystring)

print(response.text)

data = pd.DataFrame(response.text)
print(data)
