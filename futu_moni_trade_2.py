import futu as ft
import pandas as pd
import numpy as np
import time


def IFELSE(condition, true_value, false_value):
    """
    实现类似通达信IFELSE的条件判断函数
    """
    return np.where(condition, true_value, false_value)


def ISLASTBAR(df):
    """
    判断是否为最后一个Bar
    """
    return np.arange(len(df)) == len(df) - 1


def VALUEWHEN(condition, value):
    """
    获取满足条件时的值
    """
    result = np.full_like(value, np.nan)
    mask = condition
    result[mask] = value[mask]
    return result


def wavetrader(df):
    """
    Implements the WaveTrader strategy based on the given formula.

    Parameters:
    -----------
    df : pandas.DataFrame
        DataFrame with OHLC price data

    Returns:
    --------
    pandas.DataFrame
        DataFrame with original data and additional WaveTrader indicators
    """
    # Make a copy of the input DataFrame to avoid modifying the original
    result_df = df.copy()

    # Get required price data
    high = df['high'].values
    low = df['low'].values
    close = df['close'].values
    open_price = df['open'].values

    # Create shifted versions of high and low
    high_1 = np.roll(high, 1)
    high_2 = np.roll(high, 2)
    low_1 = np.roll(low, 1)
    low_2 = np.roll(low, 2)

    # Set first two elements to NaN since they don't have 2 previous values
    # Convert to float type
    high_1 = high_1.astype(float)
    high_2 = high_2.astype(float)
    low_1 = low_1.astype(float)
    low_2 = low_2.astype(float)
    # Now you can assign NaN
    high_1[:2] = np.nan
    high_2[:2] = np.nan
    low_1[:2] = np.nan
    low_2[:2] = np.nan

    # HH1: IFELSE(H<REF(H,1)&&REF(H,1)<REF(H,2),REF(H,2),0)
    # Detect two consecutive lower highs
    hh1_cond = (high < high_1) & (high_1 < high_2)
    hh1 = IFELSE(hh1_cond, high_2, 0)

    # LL1: IFELSE(L>REF(L,1)&&REF(L,1)>REF(L,2),REF(L,2),0)
    # Detect two consecutive higher lows
    ll1_cond = (low > low_1) & (low_1 > low_2)
    ll1 = IFELSE(ll1_cond, low_2, 0)

    # HH2: VALUEWHEN(HH1>0,HH1)
    # Remember the last significant high
    hh2 = VALUEWHEN(hh1 > 0, hh1)

    # LL2: VALUEWHEN(LL1>0,LL1)
    # Remember the last significant low
    ll2 = VALUEWHEN(ll1 > 0, ll1)

    # K1: IFELSE(CLOSE>HH2,-3,IFELSE(CLOSE<LL2,1,0))
    # Generate initial signal: -3 for buy (close above resistance), 1 for sell (close below support)
    k1 = IFELSE(close > hh2, -3, IFELSE(close < ll2, 1, 0))

    # K2: VALUEWHEN(K1<>0,K1),NODRAW
    # Remember the last non-zero signal
    k2 = VALUEWHEN(k1 != 0, k1)

    # G:=IFELSE(K2=1,HH2,LL2)
    # Store the reference price level (HH2 for sell signals, LL2 for buy signals)
    g = IFELSE(k2 == 1, hh2, ll2)

    # G1:=VALUEWHEN(ISLASTBAR,G)
    # Store the last value of G
    g1 = VALUEWHEN(ISLASTBAR(df), g)

    # W1:=K2
    # Store the signal
    w1 = k2

    # W2:=OPEN-CLOSE
    # Calculate the bar range
    w2 = open_price - close

    # HT:=IFELSE(OPEN>CLOSE,OPEN,CLOSE)
    # Higher of open or close
    ht = IFELSE(open_price > close, open_price, close)

    # LT:=IFELSE(OPEN<CLOSE,OPEN,CLOSE)
    # Lower of open or close
    lt = IFELSE(open_price < close, open_price, close)

    # Generate buy and sell signals
    buy_signals = (k2 == -3)
    sell_signals = (k2 == 1)

    # 组合信号序列
    signal_sequence = np.where(buy_signals, -1, np.where(sell_signals, 1, 0))

    # Add all calculated values to the result DataFrame
    result_df['HH1'] = hh1
    result_df['LL1'] = ll1
    result_df['HH2'] = hh2
    result_df['LL2'] = ll2
    result_df['K1'] = k1
    result_df['K2'] = k2
    result_df['G'] = g
    result_df['G1'] = g1
    result_df['W1'] = w1
    result_df['W2'] = w2
    result_df['HT'] = ht
    result_df['LT'] = lt
    result_df['BUY'] = buy_signals
    result_df['SELL'] = sell_signals
    result_df['SIGNAL_SEQUENCE'] = signal_sequence

    return result_df


class FutuTrader:
    def __init__(self,
                 host='127.0.0.1',
                 port=11111,
                 kline_type=ft.KLType.K_15M,
                 kline_num=100,
                 markets=['HK', 'US'],
                 check_interval=60):
        """
        初始化交易系统

        Parameters:
        -----------
        host : str, optional
            富途牛牛API服务器地址，默认为本地
        port : int, optional
            富途牛牛API服务器端口，默认为11111
        kline_type : ft.KLType, optional
            K线类型，默认为15分钟
        kline_num : int, optional
            获取K线数量，默认为100
        markets : list, optional
            交易市场，默认为港股和美股
        check_interval : int, optional
            检查间隔时间（秒），默认为60秒
        """
        # 初始化交易上下文
        self.quote_ctx = ft.OpenQuoteContext(host, port)

        # 参数设置
        self.kline_type = kline_type
        self.kline_num = kline_num
        self.markets = markets
        self.check_interval = check_interval

        # 账户信息
        self.cash_balance = 100000  # 初始资金

        # 持仓和订单记录
        self.positions = {}
        self.order_history = {}

    def get_optional_stocks(self):
        """
        获取自选股列表

        Returns:
        --------
        list
            自选股代码列表
        """
        stock_list = []
        for market in self.markets:
            ret, data = self.quote_ctx.get_user_security_group(market)
            if ret == ft.RET_OK:
                stock_list.extend(data['code'].tolist())

        # 打印自选股清单
        print("自选股清单:")
        for stock in stock_list:
            print(stock)

        return stock_list

    def get_stock_kline(self, stock_code):
        """
        获取股票K线数据

        Parameters:
        -----------
        stock_code : str
            股票代码

        Returns:
        --------
        pandas.DataFrame
            K线数据
        """
        ret, data = self.quote_ctx.get_market_candle(stock_code, self.kline_type, self.kline_num)
        if ret == ft.RET_OK:
            return data
        return None

    def check_account_balance(self, stock_code, trade_amount):
        """
        检查账户资金是否足够

        Parameters:
        -----------
        stock_code : str
            股票代码
        trade_amount : int
            交易数量

        Returns:
        --------
        bool
            是否有足够资金
        """
        ret, data = self.quote_ctx.get_market_snapshot([stock_code])
        if ret == ft.RET_OK:
            price = data['last_price'].values[0]
            total_cost = price * trade_amount
            return total_cost <= self.cash_balance
        return False

    def place_order(self, stock_code, trade_type, quantity):
        """
        下单交易

        Parameters:
        -----------
        stock_code : str
            股票代码
        trade_type : str
            交易类型 'BUY' 或 'SELL'
        quantity : int
            交易数量

        Returns:
        --------
        bool
            是否下单成功
        """
        ret, data = self.quote_ctx.get_market_snapshot([stock_code])
        if ret == ft.RET_OK:
            price = data['last_price'].values[0]

            # 记录订单
            if stock_code not in self.order_history:
                self.order_history[stock_code] = []

            if trade_type == 'BUY':
                if self.check_account_balance(stock_code, quantity):
                    self.order_history[stock_code].append({
                        'price': price,
                        'quantity': quantity,
                        'type': 'BUY'
                    })
                    self.cash_balance -= price * quantity

                    # 更新持仓
                    if stock_code not in self.positions:
                        self.positions[stock_code] = 0
                    self.positions[stock_code] += quantity

                    return True

            elif trade_type == 'SELL':
                # 检查是否有低于当前价格的买入记录
                buy_orders = [order for order in self.order_history.get(stock_code, [])
                              if order['type'] == 'BUY' and order['price'] < price]

                if buy_orders and stock_code in self.positions:
                    # 卖出相同数量
                    sell_quantity = min(quantity, sum(order['quantity'] for order in buy_orders))
                    self.cash_balance += price * sell_quantity

                    # 更新持仓
                    self.positions[stock_code] -= sell_quantity

                    # 从订单历史中移除已卖出的订单
                    for order in buy_orders[:sell_quantity]:
                        self.order_history[stock_code].remove(order)

                    return True

        return False

    def trade_strategy(self, stock_code):
        """
        交易策略主函数

        Parameters:
        -----------
        stock_code : str
            股票代码
        """
        # 获取K线数据
        kline_data = self.get_stock_kline(stock_code)

        if kline_data is not None:
            # 应用WaveTrader策略
            strategy_result = wavetrader(kline_data)

            # 获取最小交易单位
            ret, lot_size_data = self.quote_ctx.get_stock_base_info(stock_code)
            if ret == ft.RET_OK:
                lot_size = lot_size_data['lot_size'].values[0]

                # 信号序列分析
                signals = strategy_result['SIGNAL_SEQUENCE']

                # 判断是否发出交易信号
                if len(signals) >= 2:
                    if signals[-2] == 1 and signals[-1] == -1:  # 前一个是卖出,后一个是买入
                        quantity = lot_size * 3  # 买入数量为最小交易单位的三倍
                        self.place_order(stock_code, 'BUY', quantity)
                        print(f"{stock_code} 发出买入信号")

                    elif signals[-2] == -1 and signals[-1] == 1:  # 前一个是买入,后一个是卖出
                        quantity = lot_size * 3  # 卖出数量为最小交易单位的三倍
                        self.place_order(stock_code, 'SELL', quantity)
                        print(f"{stock_code} 发出卖出信号")

    def run_trading(self):
        """
        运行交易系统
        """
        # 获取自选股
        optional_stocks = self.get_optional_stocks()

        # 遍历自选股执行交易策略
        while True:
            for stock in optional_stocks:
                self.trade_strategy(stock)

            # 打印交易结果
            print("当前账户余额：", self.cash_balance)
            print("当前持仓：", self.positions)

            # 等待下一次检查
            time.sleep(self.check_interval)

    def __del__(self):
        """
        关闭交易上下文
        """
        self.quote_ctx.close()


# 使用示例
if __name__ == "__main__":
    trader = FutuTrader(
        kline_type=ft.KLType.K_15M,  # 15分钟K线
        kline_num=100,  # 获取100个K线数据
        check_interval=60  # 每60秒检查一次
    )
    trader.run_trading()
