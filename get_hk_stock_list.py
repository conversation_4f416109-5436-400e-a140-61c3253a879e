import time
import numpy as np
from datetime import datetime
import pandas as pd
from futu import *

hklist = pd.read_pickle('hkstock_list.pkl')




"""
   证券类型定义
   ..  py:class:: SecurityType
    ..  py:attribute:: STOCK
     股票
    ..  py:attribute:: IDX
     指数
    ..  py:attribute:: ETF
     交易所交易基金(Exchange Traded Funds)
    ..  py:attribute:: WARRANT
     港股窝轮牛熊证
    ..  py:attribute:: BOND
     债券
   ..  py:attribute:: DRVT
     期权
   ..  py:attribute:: FUTURE
     期货
    ..  py:attribute:: NONE
     未知
   """
kind_list = [SecurityType.STOCK, SecurityType.ETF, SecurityType.WARRANT, SecurityType.BOND, SecurityType.FUTURE]
# 创建行情对象
quote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)

# 获取当前年份的起始日期和当前日期
current_year = datetime.now().year
start_date = f"{current_year}-01-01"
end_date = datetime.now().strftime("%Y-%m-%d")


def get_hk_stock_list():
    """获取港股所有股票列表"""
    # ret, ls = quote_ctx.get_stock_basicinfo(Market.HK, SecurityType.STOCK)
    stock_list = []
    for kind in kind_list:
        print(kind)
        ret, ls = quote_ctx.get_stock_basicinfo(Market.HK, kind)
        if ret != RET_OK:
            print("Error:", ls)
            continue
        # for i in range(len(ls)):
        #     if '恒生' in ls.iloc[i].name:
        #         print(ls.iloc[i])

        stock_list.append(ls[['code', 'name']])

    return stock_list


hkstock_list = get_hk_stock_list()
#
# for name in hkstock_name_list:
#     if "做空" in name:
#         print(name)
#     elif "做多" in name:
#         print(name)
pd.to_pickle(hkstock_list, "hkstock_list.pkl")
# pd.to_csv(hkstock_list, "hkstock_list.csv", index=False)
quote_ctx.close()
