import json
from futu import *
import time


def load_stock_data(json_file):
    """加载JSON文件中的股票数据"""
    with open(json_file, 'r', encoding='utf-8') as f:
        data = json.load(f)
    return data


def get_all_stock_codes(data):
    """从JSON数据中提取所有不重复的股票代码"""
    stock_codes = set()
    for user in data['users']:
        for code in user['stocks'].keys():
            # 转换股票代码格式为富途格式
            market, code_num = code.split('.')
            if market == 'SH':
                futu_code = f'SH.{code_num}'
            elif market == 'SZ':
                futu_code = f'SZ.{code_num}'
            elif market == 'HK':
                futu_code = f'HK.{code_num}'
            stock_codes.add(futu_code)
    return list(stock_codes)


def add_to_futu_watchlist(quote_ctx, stock_codes):
    """添加股票到富途自选股"""
    batch_size = 5  # 每次添加的股票数量
    for i in range(0, len(stock_codes), batch_size):
        batch_codes = stock_codes[i:i + batch_size]
        try:
            ret, data = quote_ctx.modify_user_security(
                ModifyUserSecurityOp.ADD,
                code_list=batch_codes
            )
            if ret != RET_OK:
                print(f"添加股票批次 {batch_codes} 失败: {data}")
            else:
                print(f"成功添加股票批次: {batch_codes}")
            time.sleep(1)  # 增加延迟，避免请求过快
        except Exception as e:
            print(f"添加股票批次 {batch_codes} 时发生错误: {str(e)}")


def main():
    # 富途API配置
    host = 'localhost'  # 富途牛牛客户端IP
    port = 11111  # 富途牛牛客户端端口

    try:
        # 创建行情上下文
        quote_ctx = OpenQuoteContext(host=host, port=port)

        # 加载JSON数据
        data = load_stock_data('stocks.json')  # 确保JSON文件路径正确

        # 获取所有股票代码
        stock_codes = get_all_stock_codes(data)
        print(f"总共需要添加 {len(stock_codes)} 只股票")

        # 添加到自选股
        add_to_futu_watchlist(quote_ctx, stock_codes)

    except Exception as e:
        print(f"发生错误: {str(e)}")

    finally:
        # 确保关闭连接
        if 'quote_ctx' in locals():
            quote_ctx.close()


if __name__ == "__main__":
    main()