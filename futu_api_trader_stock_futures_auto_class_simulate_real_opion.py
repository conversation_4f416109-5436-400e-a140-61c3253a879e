from futu import OpenQuoteContext, OpenHKTradeContext, OpenFutureTradeContext, TrdSide, OrderType, TrdEnv, RET_OK, SecurityType
import getpass

class FutuTrader:
    def __init__(self, host='127.0.0.1', port=11111, is_simulate=True):
        self.host = host
        self.port = port
        self.is_simulate = is_simulate
        self.quote_ctx = OpenQuoteContext(host=host, port=port)
        self.trade_ctx = OpenHKTradeContext(host=host, port=port)

    def __del__(self):
        self.quote_ctx.close()
        self.trade_ctx.close()

    def unlock_trade_password(self, trade_ctx):
        if not self.is_simulate:
            password = getpass.getpass("请输入交易密码以解锁账户: ")
            ret, data = trade_ctx.unlock_trade(password)
            if ret == RET_OK:
                print("账户解锁成功")
            else:
                print(f"账户解锁失败: {data}")
                return False
        return True
    def is_futures(self, code):
        ret, data = self.quote_ctx.get_market_snapshot([code])
        if ret == RET_OK:
            if 'sec_type' in data.columns:
                return data['sec_type'][0] == SecurityType.FUTURE
            else:
                # If 'sec_type' is not available, we can try to infer from the code
                return 'HSI' in code or 'MHI' in code or "HTI" in code  # Add other futures codes as needed
        else:
            print('获取产品信息失败：', data)
            return False

    def get_lot_size(self, code):
        ret, data = self.quote_ctx.get_market_snapshot([code])
        if ret == RET_OK:
            return data['lot_size'][0]
        else:
            print('获取最小交易单位失败：', data)
            return None

    def get_current_price(self, code):
        ret, data = self.quote_ctx.get_market_snapshot([code])
        if ret == RET_OK:
            return data['last_price'][0]
        else:
            print('获取当前价格失败：', data)
            return None

    def get_price_spread(self, code):
        ret, data = self.quote_ctx.get_market_snapshot([code])
        if ret == RET_OK:
            return data['price_spread'][0]
        else:
            print('获取价格步长失败：', data)
            return None

    def round_to_tick_size(self, price, tick_size):
        return round(price / tick_size) * tick_size

    def place_order(self, code, quantity, order_side, trade_unit_multiplier):
        lot_size = self.get_lot_size(code)
        if lot_size is None:
            return

        current_price = self.get_current_price(code)
        if current_price is None:
            return

        is_futures = self.is_futures(code)
        adjusted_quantity = quantity * trade_unit_multiplier * lot_size

        tick_size = self.get_price_spread(code)
        if tick_size is None:
            return

        if order_side == 'BUY':
            trd_side = TrdSide.BUY
            price = self.round_to_tick_size(current_price * (1.01 if not is_futures else 1.001), tick_size)
        elif order_side == 'SELL':
            trd_side = TrdSide.SELL
            price = self.round_to_tick_size(current_price * (0.99 if not is_futures else 0.999), tick_size)
        else:
            print('无效的交易信号')
            return

        # Use the correct account type based on whether it's a futures contract or not
        if is_futures:
            trade_ctx = OpenFutureTradeContext(host=self.host, port=self.port)
        else:
            trade_ctx = self.trade_ctx

        try:
            # 尝试解锁账户（如果是实盘交易）
            if not self.unlock_trade_password(trade_ctx):
                print("账户解锁失败，无法下单")
                return

            trd_env = TrdEnv.SIMULATE if self.is_simulate else TrdEnv.REAL
            ret, data = trade_ctx.place_order(price=price, qty=adjusted_quantity, code=code,
                                              trd_side=trd_side, order_type=OrderType.NORMAL,
                                              trd_env=trd_env)

            if ret == RET_OK:
                print(f'下单成功: {data}')
            else:
                print(f'下单失败: {data}')
        finally:
            if is_futures:
                trade_ctx.close()

        print(f"尝试下单: 代码={code}, 数量={adjusted_quantity}, 价格={price}, 买卖方向={order_side}, 交易环境={'模拟盘' if self.is_simulate else '实盘'}")


def main():
    host = input("请输入主机地址 [默认: 127.0.0.1]: ") or '127.0.0.1'
    port = int(input("请输入端口号 [默认: 11111]: ") or '11111')

    while True:
        trade_mode = input("请选择交易模式 (1: 模拟盘, 2: 实盘) [默认: 1]: ") or '1'
        if trade_mode in ['1', '2']:
            break
        print("无效的输入，请重新选择。")

    is_simulate = (trade_mode == '1')

    trader = FutuTrader(host=host, port=port, is_simulate=is_simulate)

    code = input("请输入股票或期货代码 (例如：HK.00700 或 HK.HSImain2306) [默认: HK.HSI2410]: ").upper() or "HK.HSI2410"

    trade_unit_multiplier_input = input("请输入交易单位倍数 [默认: 1]: ")
    trade_unit_multiplier = int(trade_unit_multiplier_input) if trade_unit_multiplier_input else 1

    signal = input("请输入交易信号 (BUY/SELL) [默认: BUY]: ").upper()
    if signal not in ["BUY", "SELL"]:
        signal = "BUY"

    quantity = 1  # 这里设置为1，实际交易量将是 1 * trade_unit_multiplier * lot_size

    print(f"\n使用的参数:")
    print(f"主机地址: {host}")
    print(f"端口号: {port}")
    print(f"交易模式: {'模拟盘' if is_simulate else '实盘'}")
    print(f"代码: {code}")
    print(f"交易单位倍数: {trade_unit_multiplier}")
    print(f"交易信号: {signal}")
    print(f"基础数量: {quantity}\n")

    trader.place_order(code, quantity, signal, trade_unit_multiplier)


if __name__ == "__main__":
    main()