
from futu import *
host='**********'
# 创建行情上下文对象
quote_ctx = OpenQuoteContext(host=host, port=11111)

# 获取美股期货合约列表
# ret_code, ret_data = quote_ctx.get_stock_basicinfo(Market.US, SecurityType.FUTURE)
# print(ret_code)
# print(ret_data)


ret_code, ret_data = quote_ctx.get_stock_basicinfo(Market.HK, SecurityType.FUTURE)
# print(ret_code)
# print(ret_data)
# print(ret_code, ret_data.code.tolist())
df = ret_data[['code', 'name']]

for i  in range(len(df)):
    print(df.iloc[i])
    # print(item)

# print(ret_data.[code, name])
print(ret_data[['code', 'name']])
stocklist=ret_data.name.tolist()

for item in stocklist:
    print(item)



# 关闭行情上下文对象
quote_ctx.close()