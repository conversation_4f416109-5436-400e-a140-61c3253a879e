from futu import *
import time


class FutuTrader:
    def __init__(self, host='127.0.0.1', port=11111):
        self.host = host
        self.port = port
        self.quote_ctx = OpenQuoteContext(host=host, port=port)
        self.trade_ctx = OpenHKTradeContext(host=host, port=port)

    def __del__(self):
        self.quote_ctx.close()
        self.trade_ctx.close()

    def unlock_trade_password(self, trade_ctx):
        if not self.is_simulate:
            password = getpass.getpass("请输入交易密码以解锁账户: ")
            ret, data = trade_ctx.unlock_trade(password)
            if ret == RET_OK:
                print("账户解锁成功")
            else:
                print(f"账户解锁失败: {data}")
                return False
        return True
    def is_futures(self, code):
        ret, data = self.quote_ctx.get_market_snapshot([code])
        if ret == RET_OK:
            if 'sec_type' in data.columns:
                return data['sec_type'][0] == SecurityType.FUTURE
            else:
                # If 'sec_type' is not available, we can try to infer from the code
                return 'HSI' in code or 'MHI' in code  # Add other futures codes as needed
        else:
            print('获取产品信息失败：', data)
            return False
    def get_lot_size(self, code):
        ret, data = self.quote_ctx.get_market_snapshot([code])
        if ret == RET_OK:
            return data['lot_size'][0]
        else:
            print('获取最小交易单位失败：', data)
            return None

    def get_current_price(self, code):
        ret, data = self.quote_ctx.get_market_snapshot([code])
        if ret == RET_OK:
            return data['last_price'][0]
        else:
            print('获取当前价格失败：', data)
            return None

    def check_position_profit(self, code):
        ret, position_data = self.trade_ctx.position_list_query(code=code, trd_env=TrdEnv.SIMULATE)
        if ret == RET_OK:
            if len(position_data) > 0:
                cost_price = position_data['cost_price'][0]
                qty = position_data['qty'][0]
                current_price = self.get_current_price(code)
                if current_price is not None:
                    is_futures = self.is_futures(code)
                    if is_futures:
                        # 期货的盈利计算需要考虑做多和做空
                        profit = (current_price - cost_price) * qty if qty > 0 else (cost_price - current_price) * abs(qty)
                    else:
                        profit = (current_price - cost_price) * qty
                    profit_percentage = (current_price / cost_price - 1) * 100 if qty > 0 else (1 - current_price / cost_price) * 100
                    return profit > 0, profit, profit_percentage
            return False, 0, 0
        else:
            print('查询持仓失败：', position_data)
            return False, 0, 0

    def get_price_spread(self, code):
        ret, data = self.quote_ctx.get_market_snapshot([code])
        if ret == RET_OK:
            return data['price_spread'][0]
        else:
            print('获取价格步长失败：', data)
            return None

    def round_to_tick_size(self, price, tick_size):
        return round(price / tick_size) * tick_size

    def place_order(self, code, quantity, order_side, trade_unit_multiplier):
        lot_size = self.get_lot_size(code)
        if lot_size is None:
            return

        current_price = self.get_current_price(code)
        if current_price is None:
            return

        is_futures = self.is_futures(code)
        adjusted_quantity = quantity * trade_unit_multiplier * lot_size

        if order_side == 'BUY':
            trd_side = TrdSide.BUY
            price = round(current_price * (1.01 if not is_futures else 1.001), 2)
        elif order_side == 'SELL':
            trd_side = TrdSide.SELL
            price = round(current_price * (0.99 if not is_futures else 0.999), 2)
        else:
            print('无效的交易信号')
            return

        # Use the correct account type based on whether it's a futures contract or not
        if is_futures:
            trade_ctx = OpenFutureTradeContext(host=self.host, port=self.port)
        else:
            trade_ctx = self.trade_ctx

        try:
            # 尝试解锁账户（如果是实盘交易）
            if not self.unlock_trade_password(trade_ctx):
                print("账户解锁失败，无法下单")
                return

            trd_env = TrdEnv.SIMULATE if self.is_simulate else TrdEnv.REAL
            ret, data = trade_ctx.place_order(price=price, qty=adjusted_quantity, code=code,
                                              trd_side=trd_side, order_type=OrderType.NORMAL,
                                              trd_env=trd_env)

            if ret == RET_OK:
                print(f'下单成功: {data}')
            else:
                print(f'下单失败: {data}')

        finally:
            if is_futures:
                trade_ctx.close()

    def place_order(self, code, quantity, order_side, trade_unit_multiplier):
        lot_size = self.get_lot_size(code)
        if lot_size is None:
            return

        current_price = self.get_current_price(code)
        if current_price is None:
            return

        is_futures = self.is_futures(code)
        adjusted_quantity = quantity * trade_unit_multiplier * lot_size

        tick_size = self.get_price_spread(code)
        if tick_size is None:
            return

        if order_side == 'BUY':
            trd_side = TrdSide.BUY
            price = self.round_to_tick_size(current_price * (1.01 if not is_futures else 1.001), tick_size)
        elif order_side == 'SELL':
            trd_side = TrdSide.SELL
            price = self.round_to_tick_size(current_price * (0.99 if not is_futures else 0.999), tick_size)
        else:
            print('无效的交易信号')
            return

        # Use the correct account type based on whether it's a futures contract or not
        if is_futures:
            trade_ctx = OpenFutureTradeContext(host=self.host, port=self.port)
        else:
            trade_ctx = self.trade_ctx

        try:
            ret, data = trade_ctx.place_order(price=price, qty=adjusted_quantity, code=code,
                                              trd_side=trd_side, order_type=OrderType.NORMAL,
                                              trd_env=TrdEnv.SIMULATE)

            if ret == RET_OK:
                print(f'下单成功: {data}')
            else:
                print(f'下单失败: {data}')
        finally:
            if is_futures:
                trade_ctx.close()

        print(f"尝试下单: 代码={code}, 数量={adjusted_quantity}, 价格={price}, 买卖方向={order_side}")
def main():
    trader = FutuTrader()
    code = input("请输入股票或期货代码 (例如：HK.00700 或 HK.HSImain2306) [默认: HK.HSI2410]: ").upper()
    if not code:
        code = "HK.HSI2410"

    trade_unit_multiplier_input = input("请输入交易单位倍数 [默认: 1]: ")
    trade_unit_multiplier = int(trade_unit_multiplier_input) if trade_unit_multiplier_input else 1

    signal = input("请输入交易信号 (BUY/SELL) [默认: BUY]: ").upper()
    if signal not in ["BUY", "SELL"]:
        signal = "BUY"

    quantity = 1  # 这里设置为1，实际交易量将是 1 * trade_unit_multiplier * lot_size

    print(f"\n使用的参数:")
    print(f"代码: {code}")
    print(f"交易单位倍数: {trade_unit_multiplier}")
    print(f"交易信号: {signal}")
    print(f"基础数量: {quantity}\n")

    trader.place_order(code, quantity, signal, trade_unit_multiplier)


if __name__ == "__main__":
    main()