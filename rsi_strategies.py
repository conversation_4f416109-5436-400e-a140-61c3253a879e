from myfunction import HLV, HHV, LLV, MID, cross, CROSS, crossup, crossdown
from tqsdk.ta import RSI
from speaktext import speak_text
from loguru import logger as mylog
from futu_api_trader_class import FutuTrader
trader = FutuTrader()

def gen_rsi_signals(rsi, klines):
    lastsig = []
    sigall = []
    sigcount = 0

    if not rsi:
        return None
    else:

        for i in range(len(rsi)):

            if i == 0 and rsi[i] > 20:
                lastsig.append(['bpk', klines.iloc[i].time_key, klines.iloc[i].close])
                # lastsig.append(['bpk', klines.iloc[i].datetime, klines.iloc[i].close])
                sigall.append('bpk')
            if i == 0 and rsi[i] < 80:
                lastsig.append(['spk', klines.iloc[i].time_key, klines.iloc[i].close])
                sigall.append('spk')
            # else:
            #     pass

            if rsi[i] > 20 and rsi[i - 1] <= 20:
                lastsig.append(['bpk', klines.iloc[i].time_key, klines.iloc[i].close])
                sigall.append('bpk')
                sigcount = 0
            elif rsi[i] < 80 and rsi[i - 1] >= 80:
                lastsig.append(['spk', klines.iloc[i].time_key, klines.iloc[i].close])
                sigall.append('spk')
                sigcount = 0
            else:
                sigcount += 1
                sigall.append(sigcount)

    return lastsig, sigall


def rsi_info_display(bars, stock, stockname, interval):
    strategy = '普渡众生'
    # speak_text('策略加载'+strategy)
    interval = interval
    rsi = RSI(bars, 6).rsi
    rsi = rsi.tolist()
    # rsiup = crossup(rsi, 20)
    # rsidown = crossdown(rsi, 80)

    (lastsig, sigall) = gen_rsi_signals(rsi, bars)
    print(lastsig[-2:])
    print(sigall[-20:])
    if len(lastsig) > 0:

        # average_signal_periods = int(len(bars) / len(lastsig))
        # print('---------------------R S I------------------------')
        # print('当前时间:', bars.iloc[-1].datetime, 'K线数量:', len(bars), '信号数:', len(lastsig), '平均信号周期:', average_signal_periods)
        # print('最近的信号:', lastsig[-1], sigall[-20:])
        # print('K线周期:', interval, 'K线数量:', len(bars), '信号数:', len(lastsig), '平均信号周期:', int(len(bars) / len(lastsig)))
        # print('信号价格:', lastsig[-1][2], '当前价:', bars.iloc[-1].close)
        # print('最后k线:', bars.iloc[-1].datetime, '收盘价:', bars.iloc[-1].close)
        # print('--------------------------------------------------')
        signal = sigall[-1]
        if signal == 'bpk' or signal == 'spk':

            signaltmp = 'BUY' if signal == 'bpk' else 'SELL'
            signaltmp_c = '买入' if signal == 'bpk' else '卖出'
            # mylog.info([strategy, interval, stock, stockname, '发出交易信号', signal])
            print(signaltmp, '价格:', lastsig[-1][2])
            mylog.info([strategy, interval, stock, stockname, signaltmp, lastsig[-1][2]])
            speak_text(f"{strategy}, {interval}, {stockname}, {signaltmp_c}, {lastsig[-1][2]}")
            # trader.place_order(stock, 1, signaltmp, trade_unit_multiplier=1)
            email_info = f"{strategy},{stockname},{interval},分钟:,发出,{signaltmp},信号,价格:, {lastsig[-1][2]} 信号时间：{lastsig[-1][1]}"
            return email_info

    else:
        print('本标的无交易信号...')
