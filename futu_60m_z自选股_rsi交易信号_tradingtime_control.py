from futu import *
import time
from datetime import datetime
from rsi_strategies import rsi_info_display
server= '**********'
quote_ctx = OpenQuoteContext(host=server, port=11111)

symbollist = []

def get_user_security_by_grouname(groupname):
    ret, data = quote_ctx.get_user_security(groupname)
    if ret == RET_OK:
        if data.shape[0] > 0:
            print(data['code'][0])
            print(data['code'].values.tolist())
    else:
        print('error:', data)
    return data['code'].values.tolist()

groupnames = ['chicang']
for groupname in groupnames:
    symbols = get_user_security_by_grouname(groupname)
    for item in symbols:
        symbollist.append(item)

knum = 1000

def is_trading_time():
    now = datetime.now()
    weekday = now.weekday()
    if weekday >= 5:  # Weekend
        return False

    hk_morning_start = now.replace(hour=9, minute=30, second=0, microsecond=0)
    hk_morning_end = now.replace(hour=12, minute=0, second=0, microsecond=0)
    hk_afternoon_start = now.replace(hour=13, minute=0, second=0, microsecond=0)
    hk_afternoon_end = now.replace(hour=16, minute=10, second=0, microsecond=0)

    cn_morning_start = now.replace(hour=9, minute=30, second=0, microsecond=0)
    cn_morning_end = now.replace(hour=11, minute=30, second=0, microsecond=0)
    cn_afternoon_start = now.replace(hour=13, minute=0, second=0, microsecond=0)
    cn_afternoon_end = now.replace(hour=15, minute=0, second=0, microsecond=0)

    is_hk_trading = (hk_morning_start <= now <= hk_morning_end) or (hk_afternoon_start <= now <= hk_afternoon_end)
    is_cn_trading = (cn_morning_start <= now <= cn_morning_end) or (cn_afternoon_start <= now <= cn_afternoon_end)

    return is_hk_trading or is_cn_trading

ret_sub, err_message = quote_ctx.subscribe(symbollist, [SubType.K_15M], subscribe_push=True)
if ret_sub == RET_OK:
    while True:
        if not is_trading_time():
            current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            print(f"{current_time} 非交易时间，等待1分钟")
            time.sleep(60)
            continue

        for code in symbollist:
            ret, data = quote_ctx.get_cur_kline(code, knum, KLType.K_15M, AuType.NONE)
            if ret == RET_OK:
                print(code, data.name.iloc[-1], data.close.iloc[-1], data.time_key.iloc[-1])
                rsi_info_display(data, code, data.name.iloc[-1], '15m')
            else:
                print('error:', data)
            time.sleep(0.0011)
        print(time.asctime(), '休息5分钟')
        time.sleep(5 * 60)
else:
    print('subscription failed', err_message)
quote_ctx.close()