from futu import *

# 创建OpenD连接
quote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)

# 创建自定义分组
group_name = "鹏华优选成长混合A"
stocks = [
    "SH.603160",  # 汇顶科技
    "SH.688235",  # 百济神州
    "SH.600707",  # 彩虹股份
    "HK.02269",   # 药明生物
    "SH.600276",  # 恒瑞医药
    "HK.01801",   # 信达生物
    "SH.688008",  # 澜起科技
    "HK.00013",   # 和黄医药
    "SZ.002185",  # 华天科技
    "HK.00981"    # 中芯国际
]

# 创建或修改分组
ret, data = quote_ctx.modify_user_security_group(group_name=group_name, stock_list=stocks, op=ModifyUserSecurityGroupOp.ADD)
if ret == RET_OK:
    print(f"Group '{group_name}' created and stocks added successfully.")
else:
    print(f"Failed to create group or add stocks: {data}")

# 关闭连接
quote_ctx.close()