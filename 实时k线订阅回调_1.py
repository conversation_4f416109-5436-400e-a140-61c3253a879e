from futu import *
import time
from datetime import datetime, time as datetime_time

class StockQuoteHandler(StockQuoteHandlerBase):
    def __init__(self):
        super(StockQuoteHandler, self).__init__()

    def on_recv_kline(self, kline_struct):
        """K线数据回调函数"""
        if kline_struct.k_type == SubType.K_1M:  # 可以根据需要修改 K线类型判断
            print(f"收到K线数据: {datetime.now()}")
            print(kline_struct.data_df)  # K线数据DataFrame

            # 在这里调用您的策略函数
            self.run_strategy(kline_struct.data_df)

    def run_strategy(self, kline_df):
        """
        策略函数
        :param kline_df: K线数据DataFrame
        """
        try:
            # 这里写入您的策略逻辑
            print("执行策略...")

            # 示例：简单的移动平均策略
            if len(kline_df) >= 20:
                ma20 = kline_df['close'].rolling(20).mean()
                current_price = kline_df['close'].iloc[-1]
                ma20_value = ma20.iloc[-1]

                if current_price > ma20_value:
                    print("价格在20日均线上方，可能是买入信号")
                else:
                    print("价格在20日均线下方，可能是卖出信号")

        except Exception as e:
            print(f"策略执行出错: {str(e)}")


def run_quote_connection():
    """
    建立行情连接并订阅K线
    """
    quote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)

    # 设置回调处理对象
    handler = StockQuoteHandler()
    quote_ctx.set_handler(handler)

    # 订阅K线数据
    stock_code = 'HK.00700'  # 这里可以修改为您要订阅的股票代码
    ret, data = quote_ctx.subscribe(stock_code, [SubType.K_1M], subscribe_push=True)

    if ret != RET_OK:
        print(f'订阅失败: {data}')
        return

    print(f'订阅成功: {stock_code}')

    # 获取最初的K线数据
    ret, data = quote_ctx.get_cur_kline(stock_code, 100, SubType.K_1M, AuType.QFQ)
    if ret == RET_OK:
        print(f'获取历史K线数据成功')
        handler.run_strategy(data)
    else:
        print(f'获取历史K线数据失败: {data}')

    return quote_ctx


def is_trading_time():
    """判断是否为交易时间"""
    current_time = datetime.now().time()
    morning_start = datetime_time(9, 30)  # 使用datetime_time而不是time
    morning_end = datetime_time(12, 0)
    afternoon_start = datetime_time(13, 0)
    afternoon_end = datetime_time(16, 0)

    # 判断是否在交易时间内
    is_morning_session = morning_start <= current_time <= morning_end
    is_afternoon_session = afternoon_start <= current_time <= afternoon_end

    return is_morning_session or is_afternoon_session


def main():
    """主函数"""
    quote_ctx = None
    try:
        quote_ctx = run_quote_connection()

        while True:
            if is_trading_time():
                print(f"交易时间，程序运行中... {datetime.now()}")
                time.sleep(1)
            else:
                print(f"非交易时间，等待中... {datetime.now()}")
                time.sleep(60)

    except Exception as e:
        print(f"程序运行出错: {str(e)}")
    finally:
        if quote_ctx:
            quote_ctx.close()


if __name__ == "__main__":
    main()