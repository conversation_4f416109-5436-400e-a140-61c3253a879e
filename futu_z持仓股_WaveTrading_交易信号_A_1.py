import json
from futu import *
import time
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from datetime import datetime
from speaktext import speak_text


class HKStockTrader:
    def __init__(self, config_file='config.json'):
        self.quote_ctx = None
        self.trd_ctx = None
        try:
            # Load configuration
            self.load_config(config_file)

            # Initialize contexts
            self.quote_ctx = OpenQuoteContext(host=self.server, port=self.port)
            self.trd_ctx = OpenSecTradeContext(
                filter_trdmarket=TrdMarket.HK,
                host=self.server,
                port=self.port,
                security_firm=SecurityFirm.FUTUSECURITIES
            )

            # Setup parameters
            self.interval = KLType.K_15M
            self.knum = 1000
            plt.rcParams['font.family'] = ['sans-serif']
            plt.rcParams['font.sans-serif'] = ['SimHei']
            plt.style.use('dark_background')

        except Exception as e:
            print(f"Initialization error: {e}")
            self.cleanup()
            raise

    def load_config(self, config_file):
        """Load configuration from JSON file"""
        try:
            with open(config_file, 'r') as f:
                config = json.load(f)
            self.server = config.get('server', 'localhost')
            self.port = config.get('port', 11111)
        except Exception as e:
            print(f"Config loading error: {e}")
            raise

    def is_hk_trading_time(self):
        """Check if current time is HK trading hours"""
        now = datetime.now()
        if now.weekday() >= 5:  # Weekend
            return False

        morning_start = now.replace(hour=9, minute=30, second=0, microsecond=0)
        morning_end = now.replace(hour=12, minute=0, second=0, microsecond=0)
        afternoon_start = now.replace(hour=13, minute=0, second=0, microsecond=0)
        afternoon_end = now.replace(hour=16, minute=10, second=0, microsecond=0)

        return ((morning_start <= now <= morning_end) or
                (afternoon_start <= now <= afternoon_end))

    def MA(self, series, n):
        """Calculate Moving Average"""
        return series.rolling(window=n).mean()

    def VALUEWHEN(self, condition, value):
        """Return value when condition is true"""
        return value[condition].reindex(value.index).ffill()

    def wave_trading(self, df):
        """Calculate wave trading indicators"""
        df['MA1'] = self.MA(df['close'], 13)
        df['HH1'] = np.where((df['high'] < df['high'].shift(1)) &
                            (df['high'].shift(1) < df['high'].shift(2)),
                            df['high'].shift(2), 0)
        df['HH2'] = self.VALUEWHEN(df['HH1'] > 0, df['HH1'])

        df['LL1'] = np.where((df['low'] > df['low'].shift(1)) &
                            (df['low'].shift(1) > df['low'].shift(2)),
                            df['low'].shift(2), 0)
        df['LL2'] = self.VALUEWHEN(df['LL1'] > 0, df['LL1'])

        df['K1'] = np.where(df['close'] > df['HH2'], -1,
                           np.where(df['close'] < df['LL2'], 1, 0))
        df['K2'] = self.VALUEWHEN(df['K1'] != 0, df['K1'])

        df['G'] = np.where(df['K2'] == 1, df['HH2'], df['LL2'])
        df['G1'] = df['G'].iloc[-1]
        df['W1'] = df['K2']
        df['W2'] = df['open'] - df['close']
        df['HT'] = np.where(df['open'] > df['close'], df['open'], df['close'])
        df['LT'] = np.where(df['open'] < df['close'], df['open'], df['close'])
        df['signal'] = np.where(df['W1'] == 1, '空', '多')

        return df

    def plot_candlestick(self, ax, df):
        """Plot candlestick chart"""
        width = 0.6
        width2 = 0.05

        up = df[df.close >= df.open]
        down = df[df.close < df.open]
        equal = df[df.close == df.open]

        ax.bar(up.index, up.close - up.open, width, bottom=up.open, color='red')
        ax.vlines(up.index, up.low, up.high, color='red', linewidth=1)

        ax.bar(down.index, down.close - down.open, width, bottom=down.open, color='green')
        ax.vlines(down.index, down.low, down.high, color='green', linewidth=1)

        ax.bar(equal.index, width2, width, bottom=equal.open - width2/2, color='cyan')
        ax.vlines(equal.index, equal.low, equal.high, color='cyan', linewidth=1)

    def plot_wave_trading(self, df, symbol, name, knum=100):
        """Plot wave trading chart"""
        plt.figure(figsize=(15, 8))
        ax = plt.gca()
        ax.set_facecolor('black')

        df_plot = df.tail(knum)
        self.plot_candlestick(ax, df_plot)

        ma1_up = np.where(df_plot['MA1'] > df_plot['MA1'].shift(1), df_plot['MA1'], np.nan)
        ma1_down = np.where(df_plot['MA1'] <= df_plot['MA1'].shift(1), df_plot['MA1'], np.nan)

        plt.plot(df_plot.index, ma1_up, color='yellow', linewidth=4)
        plt.plot(df_plot.index, ma1_down, color='cyan', linewidth=4)

        for i in range(len(df_plot) - 1):
            if df_plot['W1'].iloc[i] == 1:
                plt.plot([df_plot.index[i], df_plot.index[i]],
                        [df_plot['low'].iloc[i], df_plot['LT'].iloc[i]], color='cyan')
                plt.plot([df_plot.index[i], df_plot.index[i]],
                        [df_plot['high'].iloc[i], df_plot['HT'].iloc[i]], color='cyan')
            elif df_plot['W1'].iloc[i] == -3:
                plt.plot([df_plot.index[i], df_plot.index[i]],
                        [df_plot['low'].iloc[i], df_plot['LT'].iloc[i]], color='red')
                plt.plot([df_plot.index[i], df_plot.index[i]],
                        [df_plot['high'].iloc[i], df_plot['HT'].iloc[i]], color='red')

            if df_plot['W1'].iloc[i] == 1 and df_plot['W1'].iloc[i - 1] == 1:
                plt.plot([df_plot.index[i - 1], df_plot.index[i]],
                        [df_plot['G'].iloc[i - 1], df_plot['G'].iloc[i]], color='limegreen')
            elif df_plot['W1'].iloc[i] == -3 and df_plot['W1'].iloc[i - 1] == -3:
                plt.plot([df_plot.index[i - 1], df_plot.index[i]],
                        [df_plot['G'].iloc[i - 1], df_plot['G'].iloc[i]], color='yellow')

        plt.text(df_plot.index[-1], df_plot['G1'].iloc[-1],
                f"{df_plot['G1'].iloc[-1]:.2f}", color='cyan', fontweight='bold')

        plt.title(f"{symbol} {name} {self.interval}", color='white', fontweight='bold')
        plt.xlabel('Date', color='white')
        plt.ylabel('Price', color='white')
        plt.grid(True, color='gray', linestyle=':', alpha=0.5)
        plt.tick_params(axis='x', colors='white')
        plt.tick_params(axis='y', colors='white')
        plt.legend(['MA1 Up', 'MA1 Down'], loc='upper left',
                  facecolor='black', edgecolor='white')
        plt.tight_layout()
        plt.show()

    def get_positions(self):
        """Get current positions"""
        try:
            ret, data = self.trd_ctx.position_list_query()
            if ret == RET_OK and data.shape[0] > 0:
                return [code for code in data['code'].values.tolist() if 'HK' in code]
        except Exception as e:
            print(f"Error getting positions: {e}")
        return []

    def process_symbol(self, code):
        """Process trading signals for a symbol"""
        try:
            ret, data = self.quote_ctx.get_cur_kline(code, self.knum, self.interval, AuType.NONE)
            if ret == RET_OK:
                stock_name = data.name.iloc[-1]
                current_price = data.close.iloc[-1]
                timestamp = data.time_key.iloc[-1]
                print(f"{code} {stock_name} {current_price} {timestamp}")

                df = self.wave_trading(data)
                signals = df.signal.tolist()

                if signals[-2] != signals[-1]:
                    signal_text = f"{stock_name} 发出做{'空' if signals[-1] == '空' else '多'}信号"
                    print(signal_text)
                    speak_text(signal_text)

                # self.plot_wave_trading(df, code, stock_name)
            else:
                print('Error:', data)
        except Exception as e:
            print(f"Error processing {code}: {e}")

    def subscribe_quotes(self):
        """Subscribe to quote data"""
        if not self.quote_ctx:
            return False

        positions = self.get_positions()
        if not positions:
            return True

        ret, data = self.quote_ctx.subscribe(positions, [SubType.K_15M])
        return ret == RET_OK

    def run(self):
        """Main trading loop"""
        print("Starting trading system...")

        if not self.subscribe_quotes():
            print("Failed to subscribe quotes")
            self.cleanup()
            return

        try:
            while True:
                if not self.is_hk_trading_time():
                    current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                    print(f"{current_time} 非交易时间，等待1分钟")
                    time.sleep(60)
                    continue

                positions = self.get_positions()
                if positions:
                    for code in positions:
                        self.process_symbol(code)
                        time.sleep(0.5)

                    print(f"{time.asctime()} 休息5分钟")
                    time.sleep(300)
                else:
                    print("No positions found, waiting...")
                    time.sleep(60)

        except KeyboardInterrupt:
            print("\nTrading system stopped by user")
        except Exception as e:
            print(f"Runtime error: {e}")
        finally:
            self.cleanup()

    def cleanup(self):
        """Cleanup resources"""
        try:
            if self.quote_ctx:
                self.quote_ctx.close()
            if self.trd_ctx:
                self.trd_ctx.close()
        except Exception as e:
            print(f"Cleanup error: {e}")


if __name__ == "__main__":
    try:
        trader = HKStockTrader()
        trader.run()
    except Exception as e:
        print(f"Program error: {e}")