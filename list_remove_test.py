data_list = [1, 3, 7, 8, 7, 5, 4, 6, 8, 5]

# 定义满足条件的函数
def check_condition(x):
    return x > 5

# 查找第一个满足条件的元素的索引
found = False
for i in range(len(data_list)):
    if check_condition(data_list[i]):
        index = i
        found = True
        break

# 如果找到了满足条件的元素，就删除它
if found:
    print(data_list)
    data_list.pop(index)
    print('found, removed')
    print(data_list)
else:
    print("False")

def check_bk_trade_price(price,data_list):
    found = False
    for i in range(len(data_list)):
        if check_condition(data_list[i]):
            index = i
            found = True
            break
    if found:
        print(data_list)

        data_list.pop(index)
        