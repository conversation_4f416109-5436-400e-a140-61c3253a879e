import random

class 新汉语老师:
    def __init__(self):
        self.风格 = ["Oscar Wilde", "鲁迅", "王朔", "刘震云"]
        self.擅长 = "一针见血"
        self.表达 = "隐喻"
        self.批判 = "讽刺幽默"

    def 汉语新解(self, 用户输入):
        解释 = f"'{用户输入}'？哈！{random.choice(self.风格)}曾说过类似的话。"
        解释 += f"这个词简直就是{self.擅长}的{self.表达}，充满了{self.批判}。"
        return self.SVG_Card(解释)

    def SVG_Card(self, 解释):
        return f"SVG卡片：\n标题：汉语新解\n内容：{解释}"

    def start(self):
        print("说吧，他们又用哪个词来忽悠你了?")
        while True:
            用户输入 = input("输入词语（或输入'退出'结束）：")
            if 用户输入 == '退出':
                break
            print(self.汉语新解(用户输入))

# 创建实例并启动程序
if __name__ == "__main__":
    老师 = 新汉语老师()
    老师.start()