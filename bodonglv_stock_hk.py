import time
import numpy as np
from datetime import datetime
from futu import *

# 创建行情对象
quote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)

# 获取当前年份的起始日期和当前日期
current_year = datetime.now().year
start_date = f"{current_year}-01-01"
end_date = datetime.now().strftime("%Y-%m-%d")


def get_hk_stock_list():
    """获取港股所有股票列表"""
    ret, ls = quote_ctx.get_stock_basicinfo(Market.HK, SecurityType.STOCK)
    if ret != RET_OK:
        print("Error:", ls)
        return []
    return ls['code'].tolist()

def get_sh_stock_list():
    """获取港股所有股票列表"""
    ret, ls = quote_ctx.get_stock_basicinfo(Market.SH, SecurityType.STOCK)
    if ret != RET_OK:
        print("Error:", ls)
        return []
    return ls['code'].tolist()

def get_sz_stock_list():
    """获取港股所有股票列表"""
    ret, ls = quote_ctx.get_stock_basicinfo(Market.SZ, SecurityType.STOCK)
    if ret != RET_OK:
        print("Error:", ls)
        return []
    return ls['code'].tolist()

def get_stock_volatility(stock_code, k_type=KLType.K_DAY):
    """获取单只股票的历史数据并计算波动率"""
    ret, data, _ = quote_ctx.request_history_kline(stock_code, start=start_date, end=end_date, ktype=k_type)

    print(len(data),data)
    if ret != RET_OK:
        print(f"Failed to get history data for {stock_code}: {data}")
        return None

    # 计算收盘价收益率标准差作为波动率
    prices = data['close'].pct_change().dropna()
    volatility = np.std(prices)
    return volatility


def main(k_type=KLType.K_DAY):
    # stock_list = get_hk_stock_list()
    # sh_stock_list = get_sh_stock_list()
    # sz_stock_list = get_sz_stock_list()
    group_name = '特别关注'
    ret, data = quote_ctx.get_user_security(group_name)

    if ret == RET_OK:
        print(data)
        if data.shape[0] > 0:  # 如果自选股列表不为空
            print(data['code'][0])  # 取第一条的股票代码
            stock_list = data['code'].values.tolist()
            print(stock_list)  # 转为 list
    else:
        print('error:', data)

    # stock_list =
    #
    # stock_list = ['HK.00700', 'SH.603260', 'SZ.000001','SH.603130','SZ.159509','SZ.000683']

    volatility_list = []

    for stock_code in stock_list:
        print('now processing', stock_code)
        vol = get_stock_volatility(stock_code, k_type)
        if vol is not None:
            volatility_list.append((stock_code, vol))
        time.sleep(0.5)  # 为避免 API 限制，加一个延迟

    sorted_volatility = sorted(volatility_list, key=lambda x: x[1], reverse=True)
    print("波动率最高的10只股票\n")
    for stock_code, vol in sorted_volatility[:10]:
        print(f"Stock: {stock_code}, Volatility: {vol}")

    quote_ctx.close()


if __name__ == "__main__":
    main()
