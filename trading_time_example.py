"""
交易时间管理器使用示例
演示如何在实际交易程序中使用统一的交易时间管理组件
"""

from trading_time_manager import TradingTimeManager, Exchange, is_hk_trading_time, is_a_stock_trading_time
from datetime import datetime, timedelta
import time


def example_basic_usage():
    """基本使用示例"""
    print("=== 基本使用示例 ===")
    
    # 创建交易时间管理器
    manager = TradingTimeManager()
    
    # 检查当前各交易所是否在交易时间
    exchanges = [
        (Exchange.HK, "港股"),
        (Exchange.SSE, "上证"),
        (Exchange.SZSE, "深证"),
        (Exchange.SHFE, "上期所"),
        (Exchange.DCE, "大商所"),
        (Exchange.CZCE, "郑商所"),
        (Exchange.CFFEX, "中金所"),
        (Exchange.GFEX, "广期所")
    ]
    
    current_time = datetime.now()
    print(f"当前时间: {current_time.strftime('%Y-%m-%d %H:%M:%S %A')}")
    print()
    
    for exchange, name in exchanges:
        is_trading = manager.is_trading_time(exchange)
        status = "🟢 交易中" if is_trading else "🔴 非交易时间"
        print(f"{name:8} {status}")
        
        # 显示交易时段
        sessions = manager.get_trading_sessions(exchange)
        session_str = " | ".join([f"{s.strftime('%H:%M')}-{e.strftime('%H:%M')}" for s, e in sessions])
        print(f"{'':10} 交易时段: {session_str}")
        print()


def example_with_config_file():
    """使用配置文件的示例"""
    print("=== 使用配置文件示例 ===")
    
    # 使用配置文件创建管理器
    manager = TradingTimeManager("trading_time_config.json")
    
    # 检查港股交易时间
    is_hk_trading = manager.is_trading_time(Exchange.HK)
    print(f"港股当前{'交易中' if is_hk_trading else '非交易时间'}")
    
    # 获取下一个交易时间
    next_trading = manager.get_next_trading_time(Exchange.HK)
    if next_trading:
        print(f"港股下一个交易时间: {next_trading.strftime('%Y-%m-%d %H:%M:%S')}")
    else:
        print("港股当前正在交易中")


def example_trading_loop():
    """交易循环示例"""
    print("=== 交易循环示例 ===")
    
    manager = TradingTimeManager()
    
    def simulate_trading_strategy(exchange_name: str, exchange: Exchange):
        """模拟交易策略"""
        print(f"\n--- {exchange_name}交易策略 ---")
        
        # 检查是否在交易时间
        if not manager.is_trading_time(exchange):
            current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            print(f"{current_time} {exchange_name}非交易时间，等待中...")
            
            # 获取下一个交易时间
            next_trading = manager.get_next_trading_time(exchange)
            if next_trading:
                wait_seconds = (next_trading - datetime.now()).total_seconds()
                print(f"距离下一个交易时间还有 {wait_seconds/3600:.1f} 小时")
            return False
        
        # 在交易时间内执行策略
        print(f"{exchange_name}交易时间内，执行交易策略...")
        # 这里可以添加实际的交易逻辑
        return True
    
    # 模拟不同交易所的策略
    strategies = [
        ("港股", Exchange.HK),
        ("A股", Exchange.SSE),
        ("期货", Exchange.SHFE)
    ]
    
    for name, exchange in strategies:
        simulate_trading_strategy(name, exchange)


def example_time_checking():
    """时间检查示例"""
    print("=== 时间检查示例 ===")
    
    manager = TradingTimeManager()
    
    # 检查特定时间点
    test_times = [
        datetime(2024, 1, 15, 10, 30),  # 周一上午
        datetime(2024, 1, 15, 14, 30),  # 周一下午
        datetime(2024, 1, 15, 22, 30),  # 周一夜盘
        datetime(2024, 1, 13, 10, 30),  # 周六
    ]
    
    for test_time in test_times:
        print(f"\n检查时间: {test_time.strftime('%Y-%m-%d %H:%M:%S %A')}")
        
        # 检查各交易所
        exchanges = [Exchange.HK, Exchange.SSE, Exchange.SHFE]
        for exchange in exchanges:
            is_trading = manager.is_trading_time(exchange, test_time)
            exchange_info = manager.get_exchange_info(exchange)
            name = exchange_info.get('name', exchange.name)
            print(f"  {name}: {'交易中' if is_trading else '非交易时间'}")


def example_convenient_functions():
    """便捷函数示例"""
    print("=== 便捷函数示例 ===")
    
    # 使用便捷函数
    print(f"港股交易时间: {is_hk_trading_time()}")
    print(f"A股交易时间: {is_a_stock_trading_time()}")
    
    # 检查特定时间
    test_time = datetime(2024, 1, 15, 10, 30)  # 周一上午10:30
    print(f"\n检查时间 {test_time.strftime('%H:%M')}:")
    print(f"港股交易时间: {is_hk_trading_time(test_time)}")
    print(f"A股交易时间: {is_a_stock_trading_time(test_time)}")


def example_integration_with_existing_code():
    """与现有代码集成示例"""
    print("=== 与现有代码集成示例 ===")
    
    # 模拟现有的交易监控代码
    def stock_monitor_with_time_control():
        """带时间控制的股票监控"""
        manager = TradingTimeManager()
        
        print("开始股票监控...")
        
        # 模拟监控循环
        for i in range(3):  # 只运行3次作为示例
            # 检查港股交易时间
            if manager.is_trading_time(Exchange.HK):
                print(f"第{i+1}次监控: 港股交易时间，执行监控逻辑")
                # 这里可以添加实际的监控代码
                # 例如：获取股价、计算指标、发送信号等
            else:
                current_time = datetime.now().strftime('%H:%M:%S')
                print(f"第{i+1}次检查 {current_time}: 港股非交易时间，跳过监控")
            
            time.sleep(1)  # 模拟等待
    
    stock_monitor_with_time_control()


def main():
    """主函数，运行所有示例"""
    examples = [
        example_basic_usage,
        example_with_config_file,
        example_trading_loop,
        example_time_checking,
        example_convenient_functions,
        example_integration_with_existing_code
    ]
    
    for i, example_func in enumerate(examples, 1):
        print(f"\n{'='*50}")
        print(f"示例 {i}: {example_func.__doc__}")
        print('='*50)
        try:
            example_func()
        except Exception as e:
            print(f"示例运行出错: {e}")
        
        if i < len(examples):
            print("\n按回车键继续下一个示例...")
            input()


if __name__ == "__main__":
    main()
