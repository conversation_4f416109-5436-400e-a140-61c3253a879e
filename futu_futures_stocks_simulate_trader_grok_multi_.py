import futu as ft
from futu import *
import time
import pandas as pd
from datetime import datetime


class MultiTradingSimulator:
    def __init__(self, host='127.0.0.1', port=11111, symbols=None):
        # 默认交易品种列表
        if symbols is None:
            symbols = [
                {'type': 'futures', 'code': 'HK.HSImain'},  # 恒生指数期货
                {'type': 'futures', 'code': 'HK.HSTECHmain'},  # 恒生科技指数期货
                {'type': 'stock', 'code': 'HK.00700'},  # 腾讯
                {'type': 'stock', 'code': 'HK.01810'},  # 小米集团
                {'type': 'stock', 'code': 'HK.09988'}  # 阿里巴巴
            ]

        self.symbols = symbols  # 交易品种列表
        self.timeframe = "K_5M"  # 时间周期
        self.max_position = 10  # 最大持仓限制
        self.entry_prices = {}  # 记录每个品种的开仓价格

        # 初始化交易上下文
        self.futures_context = OpenFutureTradeContext(
            host=host, port=port, is_encrypt=None, security_firm=SecurityFirm.FUTUSECURITIES
        )
        self.stock_context = OpenHKTradeContext(
            host=host, port=port, is_encrypt=None, security_firm=SecurityFirm.FUTUSECURITIES
        )
        self.quote_context = OpenQuoteContext(host=host, port=port)

        # 账户ID
        self.futures_acc_id = None
        self.stock_acc_id = None
        self.log_file = None  # 日志文件名

        # 每种类型的基础参数
        self.trade_configs = {
            'futures': {'lot_size': 50, 'market_auth_key': 'FUTURES_SIMULATE_HK'},
            'stock': {'lot_size': 1, 'market_auth_key': 'HK'}
        }

    def log(self, message, symbol=None):
        """带时间戳的日志输出并保存到文件"""
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        acc_id = self.futures_acc_id if symbol and self.get_trade_type(symbol) == 'futures' else self.stock_acc_id
        symbol_info = f"[Symbol: {symbol}]" if symbol else ""
        log_entry = f"[{timestamp}] [Account: {acc_id or 'N/A'}] {symbol_info} [Timeframe: {self.timeframe}] {message}"
        print(log_entry)
        if self.log_file:
            with open(self.log_file, 'a', encoding='utf-8') as f:
                f.write(log_entry + '\n')

    def get_trade_type(self, symbol):
        """根据代码查找交易类型"""
        for s in self.symbols:
            if s['code'] == symbol:
                return s['type']
        return None

    def get_context(self, symbol):
        """根据品种类型返回对应上下文"""
        trade_type = self.get_trade_type(symbol)
        return self.futures_context if trade_type == 'futures' else self.stock_context

    def connect(self):
        """建立连接并订阅行情"""
        # 获取期货账户
        ret, data = self.futures_context.get_acc_list()
        if ret == RET_OK:
            self.log('期货账户列表:')
            print(data)
            for index, row in data.iterrows():
                if row['trd_env'] == 'SIMULATE' and 'FUTURES_SIMULATE_HK' in row['trdmarket_auth']:
                    self.futures_acc_id = row['acc_id']
                    self.log(f"找到期货模拟账户: {self.futures_acc_id}")
                    break

        # 获取股票账户
        ret, data = self.stock_context.get_acc_list()
        if ret == RET_OK:
            self.log('股票账户列表:')
            print(data)
            for index, row in data.iterrows():
                if row['trd_env'] == 'SIMULATE' and 'HK' in row['trdmarket_auth']:
                    self.stock_acc_id = row['acc_id']
                    self.log(f"找到股票模拟账户: {self.stock_acc_id}")
                    break

        # 设置日志文件名（不包含具体合约）
        if self.futures_acc_id or self.stock_acc_id:
            acc_id = self.futures_acc_id or self.stock_acc_id
            self.log_file = f"trading_multi_{acc_id}_{self.timeframe}.txt"
        else:
            self.log("未找到任何模拟账户，请检查权限")
            return False

        # 订阅所有品种的行情
        for symbol in self.symbols:
            ret, data = self.quote_context.subscribe(symbol['code'], SubType.K_5M)
            if ret == RET_OK:
                self.log(f"5分钟K线行情订阅成功", symbol=symbol['code'])
            else:
                self.log(f"行情订阅失败: {data}", symbol=symbol['code'])

        return True

    def get_market_data(self, symbol):
        """获取指定品种的实时市场数据"""
        ret, data = self.quote_context.get_cur_kline(symbol, num=300, ktype=SubType.K_5M)
        if ret == RET_OK:
            return data
        else:
            self.log(f"获取行情失败: {data}", symbol=symbol)
            return None

    def get_account_info(self, trade_type):
        """获取账户信息"""
        context = self.futures_context if trade_type == 'futures' else self.stock_context
        acc_id = self.futures_acc_id if trade_type == 'futures' else self.stock_acc_id
        ret, data = context.accinfo_query(trd_env=TrdEnv.SIMULATE, acc_id=acc_id)
        if ret == RET_OK:
            return data
        else:
            self.log(f"获取账户信息失败: {data}")
            return None

    def place_order(self, symbol, price, qty, direction):
        """提交真实模拟订单"""
        trade_type = self.get_trade_type(symbol)
        context = self.get_context(symbol)
        acc_id = self.futures_acc_id if trade_type == 'futures' else self.stock_acc_id

        if acc_id is None:
            self.log("未找到对应账户，无法下单", symbol=symbol)
            return

        trd_side = TrdSide.BUY if direction == 'BUY' else TrdSide.SELL
        ret, data = context.place_order(
            price=price,
            qty=qty,
            code=symbol,
            trd_side=trd_side,
            order_type=OrderType.NORMAL,
            trd_env=TrdEnv.SIMULATE,
            acc_id=acc_id
        )

        unit = '手' if trade_type == 'futures' else '股'
        if ret == RET_OK:
            self.log(f"{direction} 订单提交成功: {qty}{unit} @ {price}", symbol=symbol)
            self.log(f"订单详情: {data}", symbol=symbol)
            if direction == 'BUY':
                self.entry_prices[symbol] = price
        else:
            self.log(f"{direction} 订单提交失败: {data}", symbol=symbol)

    def simple_trading_strategy(self, symbol):
        """为指定品种执行交易策略"""
        market_data = self.get_market_data(symbol)
        if market_data is None or len(market_data) < 14:
            return

        ma13 = market_data['close'].rolling(window=13).mean()
        current_price = market_data['close'].iloc[-1]
        prev_price = market_data['close'].iloc[-2]
        current_ma13 = ma13.iloc[-1]
        prev_ma13 = ma13.iloc[-2]

        context = self.get_context(symbol)
        acc_id = self.futures_acc_id if self.get_trade_type(symbol) == 'futures' else self.stock_acc_id
        ret, position_data = context.position_list_query(code=symbol, trd_env=TrdEnv.SIMULATE, acc_id=acc_id)
        current_position = 0
        if ret == RET_OK and not position_data.empty:
            current_position = int(position_data['qty'].iloc[0])

        if prev_price <= prev_ma13 and current_price > current_ma13 and current_position < self.max_position:
            self.place_order(symbol, current_price, 1, 'BUY')
        elif prev_price >= prev_ma13 and current_price < current_ma13 and current_position > 0:
            if symbol in self.entry_prices and current_price > self.entry_prices[symbol]:
                self.place_order(symbol, current_price, current_position, 'SELL')
                self.entry_prices.pop(symbol, None)

    def run(self):
        """运行模拟交易"""
        if not self.connect():
            return

        self.log("开始模拟交易...")
        try:
            while True:
                for symbol in self.symbols:
                    self.simple_trading_strategy(symbol['code'])

                # 输出账户信息
                for trade_type in ['futures', 'stock']:
                    account_info = self.get_account_info(trade_type)
                    if account_info is not None:
                        self.log(f"{trade_type.capitalize()} 当前资金: ${account_info['cash'].iloc[0]:.2f}")
                time.sleep(300)
        except KeyboardInterrupt:
            self.log("交易模拟结束")
            self.quote_context.close()
            self.futures_context.close()
            self.stock_context.close()


if __name__ == "__main__":
    # 默认交易多个品种
    simulator = MultiTradingSimulator()
    simulator.run()

    # 自定义交易品种
    # custom_symbols = [
    #     {'type': 'futures', 'code': 'HK.HSImain'},
    #     {'type': 'stock', 'code': 'HK.00700'}
    # ]
    # simulator = MultiTradingSimulator(symbols=custom_symbols)
    # simulator.run()