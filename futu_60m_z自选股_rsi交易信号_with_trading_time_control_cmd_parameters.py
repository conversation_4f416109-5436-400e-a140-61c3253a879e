import time
import argparse
from datetime import datetime, timedelta
from futu import *
from rsi_strategies import rsi_info_display
"""
主要改进包括：

添加命令行参数支持：

-g/--groups：指定自选股分组名称，可以多个
-p/--period：指定K线周期
-n/--knum：指定获取的K线数量
-i/--interval：指定刷新间隔


支持多种K线周期：

增加 get_kline_type() 和 get_sub_type() 方法
支持1分钟到月线的多种周期


参数默认值设置：

分组默认为 'chicang'
周期默认为 '15m'
K线数量默认为 1000
刷新间隔默认为 300秒

# 使用默认参数
python stock_monitor.py

# 指定单个分组和周期
python stock_monitor.py -g mygroup -p 5m

# 指定多个分组和其他参数
python stock_monitor.py -g group1 group2 -p 30m -n 500 -i 600

# 查看帮助信息
python stock_monitor.py --help
"""

class StockQuoteMonitor:
    def __init__(self, host='127.0.0.1', port=11111):
        self.quote_ctx = OpenQuoteContext(host=host, port=port)
        self.symbollist = []

    def is_trading_time(self):
        """
        判断是否在港股交易时间
        交易时间：
        上午9:30 - 12:00
        下午13:00 - 16:00
        排除周末和法定节假日
        """
        now = datetime.now()

        # 判断是否为工作日（周一到周五）
        if now.weekday() >= 5:
            return False

        # 获取当前时间
        current_time = now.time()

        # 上午交易时段
        morning_start = datetime.strptime('09:30', '%H:%M').time()
        morning_end = datetime.strptime('12:00', '%H:%M').time()

        # 下午交易时段
        afternoon_start = datetime.strptime('13:00', '%H:%M').time()
        afternoon_end = datetime.strptime('16:00', '%H:%M').time()

        return (morning_start <= current_time <= morning_end) or \
            (afternoon_start <= current_time <= afternoon_end)

    def get_user_security_by_groupname(self, groupname):
        """获取用户自选股列表"""
        ret, data = self.quote_ctx.get_user_security(groupname)

        if ret == RET_OK and data.shape[0] > 0:
            print(f"获取 {groupname} 分组的股票列表")
            return data['code'].values.tolist()
        else:
            print(f'获取 {groupname} 分组股票失败:', data)
            return []

    def init_symbol_list(self, groupnames):
        """初始化股票列表"""
        self.symbollist = []
        for groupname in groupnames:
            symbols = self.get_user_security_by_groupname(groupname)
            self.symbollist.extend(symbols)

    def get_kline_type(self, period):
        """根据周期字符串返回对应的K线类型"""
        kline_mapping = {
            '1m': KLType.K_1M,
            '5m': KLType.K_5M,
            '15m': KLType.K_15M,
            '30m': KLType.K_30M,
            '60m': KLType.K_60M,
            'day': KLType.K_DAY,
            'week': KLType.K_WEEK,
            'month': KLType.K_MON
        }
        return kline_mapping.get(period.lower(), KLType.K_15M)

    def get_sub_type(self, period):
        """根据周期字符串返回对应的订阅类型"""
        subtype_mapping = {
            '1m': SubType.K_1M,
            '5m': SubType.K_5M,
            '15m': SubType.K_15M,
            '30m': SubType.K_30M,
            '60m': SubType.K_60M,
            'day': SubType.K_DAY,
            'week': SubType.K_WEEK,
            'month': SubType.K_MON
        }
        return subtype_mapping.get(period.lower(), SubType.K_15M)

    def monitor_stocks(self, period='15m', knum=1000, interval=5 * 60):
        """监控股票行情"""
        kline_type = self.get_kline_type(period)
        sub_type = self.get_sub_type(period)

        # 订阅K线数据
        ret_sub, err_message = self.quote_ctx.subscribe(
            self.symbollist,
            [sub_type],
            subscribe_push=True
        )

        if ret_sub != RET_OK:
            print('订阅失败:', err_message)
            return

        try:
            while True:
                # 检查是否在交易时间
                if not self.is_trading_time():
                    print("非交易时间，等待下一个交易时段...")
                    self._wait_for_next_trading_time()
                    continue

                for code in self.symbollist:
                    ret, data = self.quote_ctx.get_cur_kline(
                        code, knum, kline_type, AuType.NONE
                    )

                    if ret == RET_OK:
                        print(
                            code,
                            data.name.iloc[-1],
                            data.close.iloc[-1],
                            data.time_key.iloc[-1]
                        )
                        rsi_info_display(data, code, data.name.iloc[-1], period)
                    else:
                        print('获取K线数据错误:', data)

                    time.sleep(0.0011)  # 控制请求频率

                print(time.asctime(), f'休息{interval / 60}分钟')
                time.sleep(interval)

        except KeyboardInterrupt:
            print("程序被手动中止")
        finally:
            self.quote_ctx.close()

    def _wait_for_next_trading_time(self):
        """等待下一个交易时段"""
        now = datetime.now()

        # 判断当前时间并计算下一个交易时段
        if now.time() < datetime.strptime('09:30', '%H:%M').time():
            # 早上9:30前
            next_time = now.replace(hour=9, minute=30, second=0, microsecond=0)
        elif now.time() < datetime.strptime('12:00', '%H:%M').time():
            # 上午交易时段
            next_time = now
        elif now.time() < datetime.strptime('13:00', '%H:%M').time():
            # 中午休市期间
            next_time = now.replace(hour=13, minute=0, second=0, microsecond=0)
        elif now.time() < datetime.strptime('16:00', '%H:%M').time():
            # 下午交易时段
            next_time = now
        else:
            # 收盘后，等到第二个交易日早上9:30
            next_time = (now + timedelta(days=1)).replace(
                hour=9, minute=30, second=0, microsecond=0
            )

        wait_seconds = (next_time - now).total_seconds()
        print(f"将在 {next_time} 进行下一次监控")
        time.sleep(wait_seconds)


def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='港股行情监控程序')

    parser.add_argument(
        '-g', '--groups',
        nargs='+',
        default=['chicang'],
        help='自选股分组名称，可以指定多个，默认为 chicang'
    )

    parser.add_argument(
        '-p', '--period',
        default='15m',
        choices=['1m', '5m', '15m', '30m', '60m', 'day', 'week', 'month'],
        help='K线周期，默认为15分钟'
    )

    parser.add_argument(
        '-n', '--knum',
        type=int,
        default=1000,
        help='获取的K线数量，默认为1000'
    )

    parser.add_argument(
        '-i', '--interval',
        type=int,
        default=300,
        help='刷新间隔（秒），默认为300秒'
    )

    return parser.parse_args()


def main():
    # 解析命令行参数
    args = parse_arguments()

    # 创建监控实例
    monitor = StockQuoteMonitor()

    # 初始化股票列表
    monitor.init_symbol_list(args.groups)

    # 开始监控
    monitor.monitor_stocks(
        period=args.period,
        knum=args.knum,
        interval=args.interval
    )


if __name__ == "__main__":
    main()