from futu import *
pwd_unlock = '147369'
trd_ctx = OpenSecTradeContext(filter_trdmarket=TrdMarket.HK, host='127.0.0.1', port=11111, security_firm=SecurityFirm.FUTUSECURITIES)
ret, data = trd_ctx.unlock_trade(pwd_unlock)  # 若使用真实账户下单，需先对账户进行解锁。此处示例为模拟账户下单，也可省略解锁。
if ret == RET_OK:
    ret, data = trd_ctx.place_order(price=310.0, qty=100, code="HK.00700", trd_side=TrdSide.BUY, trd_env=TrdEnv.SIMULATE)
    ret, data = trd_ctx.place_order(price=310.0, qty=100, code="HK.00700", trd_side=TrdSide.BUY, trd_env=TrdEnv.REAL)
    if ret == RET_OK:
        print(data)
        print(data['order_id'][0])  # 获取下单的订单号
        print(data['order_id'].values.tolist())  # 转为 list
    else:
        print('place_order error: ', data)
else:
    print('unlock_trade failed: ', data)
trd_ctx.close()
