"""
统一交易时间管理组件
支持多个交易所的交易时间判断
包括：香港交易所、上海证券交易所、深圳证券交易所、上海期货交易所、
     大连商品交易所、郑州商品交易所、中国金融期货交易所、广州期货交易所
"""

from datetime import datetime, time
from enum import Enum
from typing import List, Tuple, Optional
import json
import os


class Exchange(Enum):
    """交易所枚举"""
    HK = "香港交易所"
    SSE = "上海证券交易所"
    SZSE = "深圳证券交易所"
    SHFE = "上海期货交易所"
    DCE = "大连商品交易所"
    CZCE = "郑州商品交易所"
    CFFEX = "中国金融期货交易所"
    GFEX = "广州期货交易所"


class TradingTimeManager:
    """交易时间管理器"""
    
    def __init__(self, config_file: Optional[str] = None):
        """
        初始化交易时间管理器
        
        Args:
            config_file: 配置文件路径，如果为None则使用默认配置
        """
        self.trading_schedules = self._load_config(config_file)
    
    def _load_config(self, config_file: Optional[str] = None) -> dict:
        """加载交易时间配置"""
        if config_file and os.path.exists(config_file):
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                print(f"加载配置文件失败: {e}，使用默认配置")
        
        return self._get_default_config()
    
    def _get_default_config(self) -> dict:
        """获取默认交易时间配置"""
        return {
            Exchange.HK.name: {
                "name": "香港交易所",
                "sessions": [
                    {"start": "09:30", "end": "12:00"},  # 上午
                    {"start": "13:00", "end": "16:00"}   # 下午
                ],
                "weekdays": [0, 1, 2, 3, 4],  # 周一到周五
                "timezone": "Asia/Hong_Kong"
            },
            Exchange.SSE.name: {
                "name": "上海证券交易所",
                "sessions": [
                    {"start": "09:30", "end": "11:30"},  # 上午
                    {"start": "13:00", "end": "15:00"}   # 下午
                ],
                "weekdays": [0, 1, 2, 3, 4],
                "timezone": "Asia/Shanghai"
            },
            Exchange.SZSE.name: {
                "name": "深圳证券交易所",
                "sessions": [
                    {"start": "09:30", "end": "11:30"},  # 上午
                    {"start": "13:00", "end": "15:00"}   # 下午
                ],
                "weekdays": [0, 1, 2, 3, 4],
                "timezone": "Asia/Shanghai"
            },
            Exchange.SHFE.name: {
                "name": "上海期货交易所",
                "sessions": [
                    {"start": "09:00", "end": "10:15"},  # 上午第一节
                    {"start": "10:30", "end": "11:30"},  # 上午第二节
                    {"start": "13:30", "end": "15:00"},  # 下午
                    {"start": "21:00", "end": "23:00"}   # 夜盘（部分品种）
                ],
                "weekdays": [0, 1, 2, 3, 4],
                "timezone": "Asia/Shanghai"
            },
            Exchange.DCE.name: {
                "name": "大连商品交易所",
                "sessions": [
                    {"start": "09:00", "end": "10:15"},  # 上午第一节
                    {"start": "10:30", "end": "11:30"},  # 上午第二节
                    {"start": "13:30", "end": "15:00"},  # 下午
                    {"start": "21:00", "end": "23:00"}   # 夜盘（部分品种）
                ],
                "weekdays": [0, 1, 2, 3, 4],
                "timezone": "Asia/Shanghai"
            },
            Exchange.CZCE.name: {
                "name": "郑州商品交易所",
                "sessions": [
                    {"start": "09:00", "end": "10:15"},  # 上午第一节
                    {"start": "10:30", "end": "11:30"},  # 上午第二节
                    {"start": "13:30", "end": "15:00"},  # 下午
                    {"start": "21:00", "end": "23:30"}   # 夜盘（部分品种）
                ],
                "weekdays": [0, 1, 2, 3, 4],
                "timezone": "Asia/Shanghai"
            },
            Exchange.CFFEX.name: {
                "name": "中国金融期货交易所",
                "sessions": [
                    {"start": "09:30", "end": "11:30"},  # 上午
                    {"start": "13:00", "end": "15:00"}   # 下午
                ],
                "weekdays": [0, 1, 2, 3, 4],
                "timezone": "Asia/Shanghai"
            },
            Exchange.GFEX.name: {
                "name": "广州期货交易所",
                "sessions": [
                    {"start": "09:00", "end": "10:15"},  # 上午第一节
                    {"start": "10:30", "end": "11:30"},  # 上午第二节
                    {"start": "13:30", "end": "15:00"},  # 下午
                    {"start": "21:00", "end": "23:00"}   # 夜盘（部分品种）
                ],
                "weekdays": [0, 1, 2, 3, 4],
                "timezone": "Asia/Shanghai"
            }
        }
    
    def is_trading_time(self, exchange: Exchange, check_time: Optional[datetime] = None) -> bool:
        """
        判断指定交易所是否在交易时间
        
        Args:
            exchange: 交易所枚举
            check_time: 检查时间，如果为None则使用当前时间
            
        Returns:
            bool: 是否在交易时间
        """
        if check_time is None:
            check_time = datetime.now()
        
        # 检查是否为工作日
        if not self._is_trading_day(exchange, check_time):
            return False
        
        # 检查是否在交易时段内
        return self._is_in_trading_session(exchange, check_time.time())
    
    def _is_trading_day(self, exchange: Exchange, check_time: datetime) -> bool:
        """检查是否为交易日"""
        config = self.trading_schedules.get(exchange.name, {})
        weekdays = config.get("weekdays", [0, 1, 2, 3, 4])
        return check_time.weekday() in weekdays
    
    def _is_in_trading_session(self, exchange: Exchange, check_time: time) -> bool:
        """检查是否在交易时段内"""
        config = self.trading_schedules.get(exchange.name, {})
        sessions = config.get("sessions", [])
        
        for session in sessions:
            start_time = datetime.strptime(session["start"], '%H:%M').time()
            end_time = datetime.strptime(session["end"], '%H:%M').time()
            
            if start_time <= check_time <= end_time:
                return True
        
        return False
    
    def get_trading_sessions(self, exchange: Exchange) -> List[Tuple[time, time]]:
        """
        获取指定交易所的交易时段
        
        Args:
            exchange: 交易所枚举
            
        Returns:
            List[Tuple[time, time]]: 交易时段列表
        """
        config = self.trading_schedules.get(exchange.name, {})
        sessions = config.get("sessions", [])
        
        result = []
        for session in sessions:
            start_time = datetime.strptime(session["start"], '%H:%M').time()
            end_time = datetime.strptime(session["end"], '%H:%M').time()
            result.append((start_time, end_time))
        
        return result
    
    def get_next_trading_time(self, exchange: Exchange, check_time: Optional[datetime] = None) -> Optional[datetime]:
        """
        获取下一个交易时间
        
        Args:
            exchange: 交易所枚举
            check_time: 检查时间，如果为None则使用当前时间
            
        Returns:
            Optional[datetime]: 下一个交易时间，如果当前就在交易时间则返回None
        """
        if check_time is None:
            check_time = datetime.now()
        
        if self.is_trading_time(exchange, check_time):
            return None
        
        # 简化实现：返回下一个交易日的第一个交易时段开始时间
        config = self.trading_schedules.get(exchange.name, {})
        sessions = config.get("sessions", [])
        
        if not sessions:
            return None
        
        # 获取第一个交易时段的开始时间
        first_session_start = datetime.strptime(sessions[0]["start"], '%H:%M').time()
        
        # 如果当前是交易日但不在交易时间，检查今天是否还有交易时段
        if self._is_trading_day(exchange, check_time):
            for session in sessions:
                session_start = datetime.strptime(session["start"], '%H:%M').time()
                if check_time.time() < session_start:
                    return datetime.combine(check_time.date(), session_start)
        
        # 找下一个交易日
        next_day = check_time.replace(hour=0, minute=0, second=0, microsecond=0)
        for i in range(1, 8):  # 最多查找一周
            next_day = next_day.replace(day=next_day.day + 1)
            if self._is_trading_day(exchange, next_day):
                return datetime.combine(next_day.date(), first_session_start)
        
        return None
    
    def get_exchange_info(self, exchange: Exchange) -> dict:
        """
        获取交易所信息
        
        Args:
            exchange: 交易所枚举
            
        Returns:
            dict: 交易所配置信息
        """
        return self.trading_schedules.get(exchange.name, {})
    
    def save_config(self, config_file: str):
        """
        保存当前配置到文件
        
        Args:
            config_file: 配置文件路径
        """
        try:
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(self.trading_schedules, f, ensure_ascii=False, indent=2)
            print(f"配置已保存到: {config_file}")
        except Exception as e:
            print(f"保存配置文件失败: {e}")


# 便捷函数
def is_hk_trading_time(check_time: Optional[datetime] = None) -> bool:
    """判断是否为港股交易时间"""
    manager = TradingTimeManager()
    return manager.is_trading_time(Exchange.HK, check_time)


def is_a_stock_trading_time(check_time: Optional[datetime] = None) -> bool:
    """判断是否为A股交易时间（上海/深圳证券交易所）"""
    manager = TradingTimeManager()
    return (manager.is_trading_time(Exchange.SSE, check_time) or 
            manager.is_trading_time(Exchange.SZSE, check_time))


def is_futures_trading_time(exchange: Exchange, check_time: Optional[datetime] = None) -> bool:
    """判断是否为期货交易时间"""
    if exchange not in [Exchange.SHFE, Exchange.DCE, Exchange.CZCE, Exchange.CFFEX, Exchange.GFEX]:
        raise ValueError(f"不支持的期货交易所: {exchange}")
    
    manager = TradingTimeManager()
    return manager.is_trading_time(exchange, check_time)


if __name__ == "__main__":
    # 示例用法
    manager = TradingTimeManager()
    
    print("=== 交易时间管理器示例 ===")
    
    # 检查各交易所当前是否在交易时间
    exchanges = [Exchange.HK, Exchange.SSE, Exchange.SZSE, Exchange.SHFE, 
                Exchange.DCE, Exchange.CZCE, Exchange.CFFEX, Exchange.GFEX]
    
    for exchange in exchanges:
        is_trading = manager.is_trading_time(exchange)
        exchange_info = manager.get_exchange_info(exchange)
        print(f"{exchange_info.get('name', exchange.name)}: {'交易中' if is_trading else '非交易时间'}")
    
    # 显示港股交易时段
    print(f"\n港股交易时段:")
    hk_sessions = manager.get_trading_sessions(Exchange.HK)
    for i, (start, end) in enumerate(hk_sessions, 1):
        print(f"  时段{i}: {start.strftime('%H:%M')} - {end.strftime('%H:%M')}")
    
    # 便捷函数示例
    print(f"\n便捷函数:")
    print(f"港股交易时间: {is_hk_trading_time()}")
    print(f"A股交易时间: {is_a_stock_trading_time()}")
