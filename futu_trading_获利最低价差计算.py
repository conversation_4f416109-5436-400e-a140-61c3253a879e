import math
from futu import *

def get_hk_stock_price(code):
    quote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)
    ret, data = quote_ctx.get_market_snapshot([code])
    quote_ctx.close()
    if ret == RET_OK and not data.empty:
        return data['last_price'].iloc[0]
    else:
        print('获取价格失败:', data)
        return None

def calc_futu_hk_trade_fee(price, qty):
    amount = price * qty
    commission = max(amount * 0.0003, 15)
    platform_fee = 15
    stamp_duty = max(int(amount * 0.0013 + 0.9999), 1)
    trading_fee = round(amount * 0.00005, 3)
    trading_sys_fee = round(amount * 0.00003, 3)
    transaction_levy = round(amount * 0.00002, 3)
    total_fee = commission + platform_fee + stamp_duty + trading_fee + trading_sys_fee + transaction_levy
    return total_fee

def calc_min_sell_price(buy_price, qty, target_profit_rate=0.003):
    buy_cost = buy_price * qty + calc_futu_hk_trade_fee(buy_price, qty)
    left = buy_price
    right = buy_price * 2
    for _ in range(100):
        mid = (left + right) / 2
        sell_income = mid * qty - calc_futu_hk_trade_fee(mid, qty)
        profit_rate = (sell_income - buy_cost) / buy_cost
        if profit_rate < target_profit_rate:
            left = mid
        else:
            right = mid
    return round(right, 3)

def main():
    code_input = input('请输入港股代码（如07226）：').strip()
    qty = int(input('请输入购买数量：').strip())
    code = f'HK.{code_input.zfill(5)}'
    price = get_hk_stock_price(code)
    if price is None:
        return
    buy_fee = calc_futu_hk_trade_fee(price, qty)
    min_sell_price = calc_min_sell_price(price, qty, 0.003)
    print(f"\n当前价格: {price:.3f} 港元")
    print(f"买入总费用: {buy_fee:.3f} 港元")
    print(f"若要净赚0.3%，最低卖出价需达到: {min_sell_price:.3f} 港元")

if __name__ == '__main__':
    main()