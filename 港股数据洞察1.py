import pandas as pd
from futu import *
from datetime import datetime, timedelta

def main():
    quote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)
    code = 'HK.07226'
    end_date = datetime.now().strftime('%Y-%m-%d')
    start_date = (datetime.now() - timedelta(days=365*10)).strftime('%Y-%m-%d')

    # 正确解包返回值
    ret, data = quote_ctx.request_history_kline(code, start=start_date, end=end_date, ktype=KLType.K_DAY)
    if ret != RET_OK or data is None or data.empty:
        print('获取K线失败或无数据')
        quote_ctx.close()
        return

    df = data
    df['high_low'] = df['high'] - df['low']
    df['high_open'] = df['high'] - df['open']
    df['open_low'] = df['open'] - df['low']
    df['hl_div_open'] = (df['high'] - df['low']) / df['open']

    print('最高价-最低价的平均值:', df['high_low'].mean())
    print('最高价-开盘价的平均值:', df['high_open'].mean())
    print('开盘价-最低价的平均值:', df['open_low'].mean())
    print('(最高价-最低价)/开盘价的平均值:', df['hl_div_open'].mean())

    quote_ctx.close()

if __name__ == '__main__':
    main()