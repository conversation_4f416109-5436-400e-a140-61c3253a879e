try:
    from tdx_function import HHV, LLV, REF, IF, VAL<PERSON><PERSON>H<PERSON>, BARSLAST
except ImportError:
    from .tdx_function import HHV, LLV, REF, IF, VALUEWHEN, BARSLAST

import pandas as pd
import numpy as np


def calculate_signals(HIGH: pd.Series, LOW: pd.Series, CLOSE: pd.Series):
    """
    Calculate trading signals based on price action

    Parameters:
    -----------
    HIGH : pd.Series
        High prices
    LOW : pd.Series
        Low prices
    CLOSE : pd.Series
        Close prices

    Returns:
    --------
    dict
        Dictionary containing:
        - long_stop: Long position stop loss levels
        - short_stop: Short position stop loss levels
        - G: Combined stop loss levels
        - W1: Long position signal
        - W2: Short position signal
    """

    # Create IF instance
    if_func = IF()

    # Initial calculations
    X_34 = if_func(HIGH < REF(LOW, 1), REF(LOW, 1), HIGH)
    X_35 = if_func(LOW > REF(HIGH, 1), REF(HIGH, 1), LOW)

    # Calculate HHV and LLV for 3 periods
    X_36 = HHV(X_34, 3)
    X_37 = LLV(X_35, 3)

    # Calculate BARSLAST for condition changes
    X_38 = BARSLAST(X_37 < REF(X_37, 1))
    X_39 = BARSLAST(X_36 > REF(X_36, 1))

    # Identify price extremes
    X_40 = if_func(HHV(X_34, X_39 + 1) == X_34, 1, 0)
    X_41 = if_func(LLV(X_35, X_38 + 1) == X_35, 1, 0)

    # Calculate reference periods
    X_42 = BARSLAST(X_40)
    X_43 = REF(LLV(X_35, 3), X_42)
    X_44 = BARSLAST(X_41)
    X_45 = REF(HHV(X_34, 3), X_44)

    # Track values when conditions are met
    X_46 = VALUEWHEN(X_45 > 0, X_45)
    X_47 = VALUEWHEN(X_43 > 0, X_43)

    # Generate initial signals
    X_48 = if_func(CLOSE > X_46, -1, if_func(CLOSE < X_47, 1, 0))
    X_49 = VALUEWHEN(X_48 != 0, X_48)

    # Calculate periods since signal changes
    X_50 = BARSLAST(CROSS(0, X_49))
    X_51 = BARSLAST(CROSS(X_49, 0))

    # Calculate stop levels
    X_52 = if_func(X_49 == 1,
                   if_func(LLV(X_46, X_51 + 1) == X_46, X_46, LLV(X_46, X_51 + 1)),
                   X_46)
    X_53 = if_func(X_49 == -1,
                   if_func(HHV(X_47, X_50 + 1) == X_47, X_47, HHV(X_47, X_50 + 1)),
                   X_47)

    # Generate final signals
    X_54 = if_func(CLOSE > X_52, -1, if_func(CLOSE < X_53, 1, 0))
    X_55 = VALUEWHEN(X_54 != 0, X_54)

    # Calculate final reference periods
    X_56 = BARSLAST(CROSS(0, X_54))
    X_57 = BARSLAST(CROSS(X_54, 0))

    # Calculate final stop level
    X_58 = if_func(X_55 == 1,
                   if_func(LLV(X_52, X_57 + 1) == X_52, X_52, LLV(X_52, X_57 + 1)),
                   if_func(HHV(X_53, X_56 + 1) == X_53, X_53, HHV(X_53, X_56 + 1)))

    # Calculate output signals
    long_stop = if_func(X_55 < 0, X_58, np.nan)  # 多头止损
    short_stop = if_func(X_55 > 0, X_58, np.nan)  # 空头止损
    G = X_58
    W1 = X_55 < 0
    W2 = X_55 > 0

    return {
        'long_stop': long_stop,
        'short_stop': short_stop,
        'G': G,
        'W1': W1,
        'W2': W2
    }

#
# def CROSS(series1, series2):
#     """
#     Helper function to detect crossover
#     """
#     return (series1 > series2) & (series1.shift(1) <= series2.shift(1))
#

def CROSS(series1, series2):
    """
    Helper function to detect crossover between two series or between a series and a scalar

    Parameters:
    -----------
    series1 : pd.Series or scalar
        First series or scalar value
    series2 : pd.Series or scalar
        Second series or scalar value

    Returns:
    --------
    pd.Series
        Boolean series indicating where crossover occurred
    """
    # Convert scalar to Series if needed
    if isinstance(series1, (int, float)):
        if isinstance(series2, pd.Series):
            series1 = pd.Series([series1] * len(series2), index=series2.index)
    elif isinstance(series2, (int, float)):
        if isinstance(series1, pd.Series):
            series2 = pd.Series([series2] * len(series1), index=series1.index)

    # Now both are Series, we can perform the comparison
    return (series1 > series2) & (series1.shift(1) <= series2.shift(1))


df =pd.read_csv('180.csv')
df.columns = df.columns.str.upper()
df = calculate_signals(df.HIGH, df.LOW, df.CLOSE)
print(df)
print(df['G'].iloc[-1])
