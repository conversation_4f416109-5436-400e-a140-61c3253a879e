try:
    from tdx_function import HHV, LLV, REF, IF, VA<PERSON><PERSON><PERSON>H<PERSON>, BARSLAST
except ImportError:
    from .tdx_function import HHV, LLV, REF, IF, VALUEWHEN, BARSLAST

import pandas as pd
import numpy as np


def calculate_signals(results):
    """
    Calculate trading signals based on price action

    Parameters:
    -----------
    HIGH : pd.Series
        High prices
    LOW : pd.Series
        Low prices
    CLOSE : pd.Series
        Close prices

    Returns:
    --------
    pd.DataFrame
        DataFrame containing all signals and intermediate calculations
    """

    # Create IF instance
    if_func = IF()

    # Create a DataFrame to store all results
    # results = df

    # Store input data
    HIGH = results['high']
    LOW = results['low']
    CLOSE = results['close']

    # Initial calculations
    results['X_34'] = if_func(HIGH < REF(LOW, 1), REF(LOW, 1), HIGH)
    results['X_35'] = if_func(LOW > REF(HIGH, 1), REF(HIGH, 1), LOW)

    # Calculate HHV and LLV for 3 periods
    results['X_36'] = HHV(results['X_34'], 3)
    results['X_37'] = LLV(results['X_35'], 3)

    # Calculate BARSLAST for condition changes
    results['X_38'] = BARSLAST(results['X_37'] < REF(results['X_37'], 1))
    results['X_39'] = BARSLAST(results['X_36'] > REF(results['X_36'], 1))

    # Identify price extremes
    results['X_40'] = if_func(HHV(results['X_34'], results['X_39'] + 1) == results['X_34'], 1, 0)
    results['X_41'] = if_func(LLV(results['X_35'], results['X_38'] + 1) == results['X_35'], 1, 0)

    # Calculate reference periods
    results['X_42'] = BARSLAST(results['X_40'])
    results['X_43'] = REF(LLV(results['X_35'], 3), results['X_42'])
    results['X_44'] = BARSLAST(results['X_41'])
    results['X_45'] = REF(HHV(results['X_34'], 3), results['X_44'])

    # Track values when conditions are met
    results['X_46'] = VALUEWHEN(results['X_45'] > 0, results['X_45'])
    results['X_47'] = VALUEWHEN(results['X_43'] > 0, results['X_43'])

    # Generate initial signals
    results['X_48'] = if_func(CLOSE > results['X_46'], -1,
                              if_func(CLOSE < results['X_47'], 1, 0))
    results['X_49'] = VALUEWHEN(results['X_48'] != 0, results['X_48'])

    # Calculate periods since signal changes
    results['X_50'] = BARSLAST(CROSS(0, results['X_49']))
    results['X_51'] = BARSLAST(CROSS(results['X_49'], 0))

    # Calculate stop levels
    results['X_52'] = if_func(results['X_49'] == 1,
                              if_func(LLV(results['X_46'], results['X_51'] + 1) == results['X_46'],
                                      results['X_46'],
                                      LLV(results['X_46'], results['X_51'] + 1)),
                              results['X_46'])

    results['X_53'] = if_func(results['X_49'] == -1,
                              if_func(HHV(results['X_47'], results['X_50'] + 1) == results['X_47'],
                                      results['X_47'],
                                      HHV(results['X_47'], results['X_50'] + 1)),
                              results['X_47'])

    # Generate final signals
    results['X_54'] = if_func(CLOSE > results['X_52'], -1,
                              if_func(CLOSE < results['X_53'], 1, 0))
    results['X_55'] = VALUEWHEN(results['X_54'] != 0, results['X_54'])

    # Calculate final reference periods
    results['X_56'] = BARSLAST(CROSS(0, results['X_54']))
    results['X_57'] = BARSLAST(CROSS(results['X_54'], 0))

    # Calculate final stop level
    results['X_58'] = if_func(results['X_55'] == 1,
                              if_func(LLV(results['X_52'], results['X_57'] + 1) == results['X_52'],
                                      results['X_52'],
                                      LLV(results['X_52'], results['X_57'] + 1)),
                              if_func(HHV(results['X_53'], results['X_56'] + 1) == results['X_53'],
                                      results['X_53'],
                                      HHV(results['X_53'], results['X_56'] + 1)))

    # Calculate output signals
    results['long_stop'] = if_func(results['X_55'] < 0, results['X_58'], np.nan)  # 多头止损
    results['short_stop'] = if_func(results['X_55'] > 0, results['X_58'], np.nan)  # 空头止损
    results['G'] = results['X_58']
    results['long'] = results['X_55'] < 0  # 多头信号
    results['short'] = results['X_55'] > 0  # 空头信号
    results['signal'] = np.where(results['short'] == 1, '空', '多')
    return results

#
# def CROSS(series1, series2):
#     """
#     Helper function to detect crossover between two series or between a series and a scalar
#     """
#     # Convert scalar to Series if needed
#     if isinstance(series1, (int, float)):
#         if isinstance(series2, pd.Series):
#             series1 = pd.Series([series1] * len(series2), index=series2.index)
#     elif isinstance(series2, (int, float)):
#         if isinstance(series1, pd.Series):
#             series2 = pd.Series([series2] * len(series1), index=series1.index)
#
#     return (series1 > series2) & (series1.shift(1) <= series2.shift(1))
#

import pandas as pd


def CROSS(series1: pd.Series | float | int, series2: pd.Series | float | int) -> pd.Series:
    """
    检测两个序列或一个序列与标量之间的交叉点。

    交叉定义：在某个时间点，series1 > series2，且在前一个时间点，series1 <= series2。

    参数：
    series1 (pd.Series | float | int): 第一个序列或标量。
    series2 (pd.Series | float | int): 第二个序列或标量。

    返回：
    pd.Series: 布尔序列，表示每个时间点的交叉情况。

    抛出：
    ValueError: 如果 series1 和 series2 都不是 pandas Series。
    """
    # 情况 1：两者均为 Series
    if isinstance(series1, pd.Series) and isinstance(series2, pd.Series):
        if not series1.index.equals(series2.index):
            raise ValueError("两个序列的索引必须相同。")
        return (series1 > series2) & (series1.shift(1) <= series2.shift(1))

    # 情况 2：series1 是 Series，series2 是标量
    elif isinstance(series1, pd.Series) and isinstance(series2, (int, float)):
        return (series1 > series2) & (series1.shift(1) <= series2)

    # 情况 3：series1 是标量，series2 是 Series
    elif isinstance(series1, (int, float)) and isinstance(series2, pd.Series):
        return (series1 > series2) & (series1 <= series2.shift(1))

    # 情况 4：两者都不是 Series
    else:
        raise ValueError("series1 和 series2 中至少有一个必须是 pandas Series。")

if __name__ == '__main__':
    # datafile = 'data_OI_15.csv'
    # df = pd.read_csv(datafile)
    # df.columns = df.columns.str.upper()
    # df = calculate_signals(df)
    # print(df)
    # print(df['G'].iloc[-1])
    import pandas as pd

    # 示例 1：series1 是 Series，series2 是标量
    s1 = pd.Series([1, 2, 3, 2, 1])
    print(CROSS(s1, 2))
    # 输出：0    False
    #       1     True
    #       2    False
    #       3    False
    #       4    False
    #       dtype: bool

    # 示例 2：series1 是标量，series2 是 Series
    s2 = pd.Series([1, 2, 1, 2, 3])
    print(CROSS(2, s2))
    # 输出：0    False
    #       1    False
    #       2     True
    #       3    False
    #       4    False
    #       dtype: bool

    # 示例 3：两者均为 Series
    s3 = pd.Series([1, 2, 3, 2, 1])
    s4 = pd.Series([2, 1, 2, 3, 2])
    print(CROSS(s3, s4))
    # 输出：0    False
    #       1     True
    #       2    False
    #       3    False
    #       4    False
    #       dtype: bool