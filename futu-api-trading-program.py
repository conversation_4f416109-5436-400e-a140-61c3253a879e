from futu import *
import time

class FutuTrader:
    def __init__(self, host='127.0.0.1', port=11111):
        self.quote_ctx = OpenQuoteContext(host=host, port=port)
        # 使用模拟交易环境
        self.trade_ctx = OpenHKTradeContext(host=host, port=port, security_firm=SecurityFirm.FUTUSECURITIES, 
                                            trd_env=TrdEnv.SIMULATE)
        
    def __del__(self):
        self.quote_ctx.close()
        self.trade_ctx.close()
    
    def get_lot_size(self, code):
        ret, data = self.quote_ctx.get_market_snapshot([code])
        if ret == RET_OK:
            return data['lot_size'][0]
        else:
            print('获取最小交易单位失败：', data)
            return None

    def get_current_price(self, code):
        ret, data = self.quote_ctx.get_market_snapshot([code])
        if ret == RET_OK:
            return data['last_price'][0]
        else:
            print('获取当前价格失败：', data)
            return None

    def place_order(self, code, quantity, order_side, trade_unit_multiplier):
        lot_size = self.get_lot_size(code)
        if lot_size is None:
            return
        
        current_price = self.get_current_price(code)
        if current_price is None:
            return
        
        adjusted_quantity = quantity * trade_unit_multiplier * lot_size
        
        if order_side == 'BUY':
            trd_side = TrdSide.BUY
            # 买入价格略高于当前价格，提高成交概率
            price = round(current_price * 1.01, 2)
        elif order_side == 'SELL':
            trd_side = TrdSide.SELL
            # 卖出价格略低于当前价格，提高成交概率
            price = round(current_price * 0.99, 2)
        else:
            print('无效的交易信号')
            return

        if trd_side == TrdSide.SELL:
            # 检查持仓
            ret, position_data = self.trade_ctx.position_list_query(code=code, trd_env=TrdEnv.SIMULATE)
            if ret == RET_OK:
                if len(position_data) == 0 or position_data['qty'][0] < adjusted_quantity:
                    print('持仓不足，无法卖出')
                    return
            else:
                print('查询持仓失败：', position_data)
                return

        ret, data = self.trade_ctx.place_order(price=price, qty=adjusted_quantity, code=code, 
                                               trd_side=trd_side, order_type=OrderType.NORMAL, 
                                               trd_env=TrdEnv.SIMULATE)
        
        if ret == RET_OK:
            print(f'下单成功: {data}')
        else:
            print(f'下单失败: {data}')

def main():
    trader = FutuTrader()
    
    code = input("请输入股票代码 (例如：HK.00700): ")
    trade_unit_multiplier = int(input("请输入交易单位倍数: "))
    signal = input("请输入交易信号 (BUY/SELL): ").upper()
    
    quantity = 1  # 这里设置为1，实际交易量将是 1 * trade_unit_multiplier * lot_size
    
    trader.place_order(code, quantity, signal, trade_unit_multiplier)

if __name__ == "__main__":
    main()
