import time
from futu import *


class CurKlineTest(CurKlineHandlerBase):
    def on_recv_rsp(self, rsp_str):
        ret_code, data = super(CurKlineTest, self).on_recv_rsp(rsp_str)
        if ret_code != RET_OK:
            print("CurKlineTest: error, msg: %s" % data)
            return RET_ERROR, data

        print("CurKlineTest ", data)  # CurKlineTest自己的处理逻辑

        return RET_OK, data


quote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)
handler = CurKlineTest()
quote_ctx.set_handler(handler)
quote_ctx.subscribe(['HK.00700'], [SubType.K_1M])
while True:
    time.sleep(15)

quote_ctx.close()
