from futu import *

trd_ctx = OpenSecTradeContext(filter_trdmarket=TrdMarket.HK, host='127.0.0.1', port=11111,
                              security_firm=SecurityFirm.FUTUSECURITIES)
ret, data = trd_ctx.position_list_query()
if ret == RET_OK:
    print(data)
    if data.shape[0] > 0:  # 如果持仓列表不为空
        print(data['stock_name'][0])  # 获取持仓第一个股票名称
        print(data['stock_name'].values.tolist())  # 转为 list
else:
    print('position_list_query error: ', data)
trd_ctx.close()  # 关闭当条连接

# 查询当日交易
trd_ctx = OpenSecTradeContext(filter_trdmarket=TrdMarket.HK, host='127.0.0.1', port=11111, security_firm=SecurityFirm.FUTUSECURITIES)
ret, data = trd_ctx.deal_list_query()
if ret == RET_OK:
    print(data)
    if data.shape[0] > 0:  # 如果成交列表不为空
        print(data['order_id'][0])  # 获取当日成交的第一个订单号
        print(data['order_id'].values.tolist())  # 转为 list
        ret2, data2 = trd_ctx.order_fee_query(data['order_id'].values.tolist())
        if ret2 == RET_OK:
            print(data2)
else:
    print('deal_list_query error: ', data)
trd_ctx.close()


# 查询订单费用

trd_ctx = OpenSecTradeContext(filter_trdmarket=TrdMarket.US, host='127.0.0.1', port=11111, security_firm=SecurityFirm.FUTUSECURITIES)
ret1, data1 = trd_ctx.history_order_list_query(status_filter_list=[OrderStatus.FILLED_ALL])
if ret1 == RET_OK:
    if data1.shape[0] > 0:  # 如果订单列表不为空
        ret2, data2 = trd_ctx.order_fee_query(data1['order_id'].values.tolist())  # 将订单 id 转为 list，查询订单费用
        if ret2 == RET_OK:
            print(data2)
            print(data2['fee_details'][0])# 打印第一笔订单的收费明细
            print(data2['fee_details'].tolist()[-1]) # 打印最后一笔订单的收费明细
            print(data2.fee_amount.sum())
        else:
            print('order_fee_query error: ', data2)
else:
    print('order_list_query error: ', data1)
trd_ctx.close()

# 解锁交易
pwd_unlock = '147369'
trd_ctx = OpenSecTradeContext(filter_trdmarket=TrdMarket.HK, host='127.0.0.1', port=11111,
                              security_firm=SecurityFirm.FUTUSECURITIES)
ret, data = trd_ctx.unlock_trade(pwd_unlock)
if ret == RET_OK:
    print('unlock success!')
else:
    print('unlock_trade failed: ', data)
trd_ctx.close()


# 下单
# pwd_unlock = '147369'
# trd_ctx = OpenSecTradeContext(filter_trdmarket=TrdMarket.HK, host='127.0.0.1', port=11111, security_firm=SecurityFirm.FUTUSECURITIES)
# ret, data = trd_ctx.unlock_trade(pwd_unlock)  # 若使用真实账户下单，需先对账户进行解锁。此处示例为模拟账户下单，也可省略解锁。
# if ret == RET_OK:
#     print('unlock_trade successful: ', data)

# if ret == RET_OK:
#     # ret, data = trd_ctx.place_order(price=510.0, qty=100, code="HK.00700", trd_side=TrdSide.BUY, trd_env=TrdEnv.SIMULATE)
#     ret, data = trd_ctx.place_order(price=13.0, qty=1000, code="HK.00358", trd_side=TrdSide.BUY, trd_env=TrdEnv.REAL)
#
#     if ret == RET_OK:
#         print(data)
#         print(data['order_id'][0])  # 获取下单的订单号
#         print(data['order_id'].values.tolist())  # 转为 list
#         ret2, data2 = trd_ctx.order_fee_query(data['order_id'].values.tolist())  # 将订单 id 转为 list，查询订单费用
#         if ret2 == RET_OK:
#             print(data2)
#
#     else:
#         print('place_order error: ', data)
# else:
#     print('unlock_trade failed: ', data)
# trd_ctx.close()


# 查询未完成订单
trd_ctx = OpenSecTradeContext(filter_trdmarket=TrdMarket.HK, host='127.0.0.1', port=11111, security_firm=SecurityFirm.FUTUSECURITIES)
# ret, data = trd_ctx.order_list_query(status_filter_list=[OrderStatus.FILLED_PART, OrderStatus.FILLED_ALL])
ret, data = trd_ctx.order_list_query()
if ret == RET_OK:
    print(data)
    if data.shape[0] > 0:  # 如果订单列表不为空
        print(data['order_id'][0])  # 获取未完成订单的第一个订单号
        print(data['order_id'].values.tolist())  # 转为 list
        ret2, data2 = trd_ctx.order_fee_query(data['order_id'].values.tolist())
        if ret2 == RET_OK:
            print(data2)
else:
    print('order_list_query error: ', data)
trd_ctx.close()


