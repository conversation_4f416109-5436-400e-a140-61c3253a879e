from futu import *
import time
from datetime import datetime
# from rsi_strategies import rsi_info_display
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from speaktext import speak_text
# from trading_time_manager import TradingTimeManager, Exchange

server = "localhost"
port = 11111

plt.rcParams['font.family'] = ['sans-serif']
plt.rcParams['font.sans-serif'] = ['SimHei']
quote_ctx = OpenQuoteContext(host=server, port=port)

trd_ctx = OpenSecTradeContext(filter_trdmarket=TrdMarket.HK, host='127.0.0.1', port=11111,
                              security_firm=SecurityFirm.FUTUSECURITIES)

symbollist = []
interval = KLType.K_15M


def is_trading_time():
    """
    判断是否在港股交易时间
    交易时间：
    上午9:30 - 12:00
    下午13:00 - 16:00
    排除周末
    """
    now = datetime.now()

    # 判断是否为工作日（周一到周五）
    if now.weekday() >= 5:
        return False

    # 获取当前时间
    current_time = now.time()

    # 港股交易时段
    morning_start = datetime.strptime("09:30", '%H:%M').time()
    morning_end = datetime.strptime("12:00", '%H:%M').time()
    afternoon_start = datetime.strptime("13:00", '%H:%M').time()
    afternoon_end = datetime.strptime("16:00", '%H:%M').time()

    return (morning_start <= current_time <= morning_end) or \
           (afternoon_start <= current_time <= afternoon_end)


def count_same_from_right(arr):
    if not arr:  # 如果数组为空，返回0
        return 0

    last_element = arr[-1]  # 获取最后一个元素
    count = 0

    # 从右向左遍历数组
    for i in range(len(arr) - 1, -1, -1):
        if arr[i] == last_element:
            count += 1
        else:
            break  # 一旦遇到不同的元素，就停止计数

    return count


def MA(series, n):
    return series.rolling(window=n).mean()


def VALUEWHEN(condition, value):
    return value[condition].reindex(value.index).ffill()


def wave_trading(df):
    # 计算指标

    df['MA1'] = MA(df['close'], 13)
    df['HH1'] = np.where((df['high'] < df['high'].shift(1)) & (df['high'].shift(1) < df['high'].shift(2)),
                         df['high'].shift(2), 0)
    df['HH2'] = VALUEWHEN(df['HH1'] > 0, df['HH1'])

    df['LL1'] = np.where((df['low'] > df['low'].shift(1)) & (df['low'].shift(1) > df['low'].shift(2)), df['low'].shift(2),
                         0)
    df['LL2'] = VALUEWHEN(df['LL1'] > 0, df['LL1'])

    df['K1'] = np.where(df['close'] > df['HH2'], -1, np.where(df['close'] < df['LL2'], 1, 0))
    df['K2'] = VALUEWHEN(df['K1'] != 0, df['K1'])

    df['G'] = np.where(df['K2'] == 1, df['HH2'], df['LL2'])
    df['G1'] = df['G'].iloc[-1]

    df['W1'] = df['K2']
    df['W2'] = df['open'] - df['close']
    df['HT'] = np.where(df['open'] > df['close'], df['open'], df['close'])
    df['LT'] = np.where(df['open'] < df['close'], df['open'], df['close'])
    df['signal'] = np.where(df['W1'] == 1, '空', '多')

    return df


def wave_signal(df):
    strategy_name = '突破火线'
    # 随风摇摆ma 普渡众生rsi 突破火线 wave
    symbol = df.code.iloc[-1]
    symbol_name = df.name.iloc[-1]
    df = wave_trading(df)
    signals = df.signal.tolist()
    signal_last = count_same_from_right(signals)
    signal_price = df.close.iloc[-signal_last]
    current_price = df.close.iloc[-1]
    current_signal = signals[-1]
    pnl = current_price - signal_price if current_signal == '多' else signal_price - current_price
    print(f"{symbol}_{symbol_name}: current_signal:{current_signal}, Current price: {current_price}, signal_price: {signal_price}, signal_last: {signal_last}, pnl: {pnl}")
    print(f"持续周期:{count_same_from_right(signals)}")
    print(signals[-35:])

    if signals[-2] == '多' and signals[-1] == '空':
        print(f"{symbol_name}{strategy_name} 发出做空信号")
        speak_text(f"{symbol_name}{strategy_name} 发出做空信号")
        # positions = trader.get_position(symbol)
        # pos_short = positions['SHORT']
        # if abs(pos_short) < pos_short_limit:
        #     trader.open_short(symbol, quantity)
        #     print(f"满足下空单条件,下空单{quantity}")
        # if long_pnl_ratio > 20 and pos_long > quantity:
        #     trader.close_long(symbol, quantity)
        #     print(f"平仓多单, {symbol}: 数量:{quantity}, 价格:{current_price}")
        # else:
        #     print(f"平仓条件不满足.")
    if signals[-2] == '空' and signals[-1] == '多':
        print(f"{symbol_name}{strategy_name} 发出做多信号")
        speak_text(f"{symbol_name}{strategy_name} 发出做多信号")

    return df

def draw_wave_trading(df, knum):
    symbol = df.code.iloc[0]
    name = df.name.iloc[0]
    # 仅保留最新knum条数据
    df = df.iloc[-knum:]

    # 设置绘图风格
    plt.style.use('dark_background')
    fig, ax = plt.subplots(figsize=(15, 10))

    # 绘制K线图

    def plot_candlestick(ax, df):

        width = 0.6
        width2 = 0.05

        up = df[df.close > df.open]
        down = df[df.close < df.open]
        equal = df[df.close == df.open]

        # 上涨K线 - 现在是红色
        ax.bar(up.index, up.close - up.open, width, bottom=up.open, color='red')
        ax.vlines(up.index, up.low, up.high, color='red', linewidth=1)

        # 下跌K线 - 现在是绿色
        ax.bar(down.index, down.close - down.open, width, bottom=down.open, color='green')
        ax.vlines(down.index, down.low, down.high, color='green', linewidth=1)

        # 开盘价等于收盘价的K线
        ax.bar(equal.index, width2, width, bottom=equal.open - width2 / 2, color='cyan')
        ax.vlines(equal.index, equal.low, equal.high, color='cyan', linewidth=1)

    plot_candlestick(ax, df)

    # 绘制MA1线
    # df['MA1'] = MA(df['close'], 13)
    ma1_up = np.where(df['MA1'] > df['MA1'].shift(1), df['MA1'], np.nan)
    ma1_down = np.where(df['MA1'] <= df['MA1'].shift(1), df['MA1'], np.nan)

    plt.plot(df.index, ma1_up, color='yellow', linewidth=4)
    plt.plot(df.index, ma1_down, color='cyan', linewidth=4)

    # 绘制其他线条
    for i in range(len(df) - 1):
        if df['W1'].iloc[i] == 1:
            plt.plot([df.index[i], df.index[i]], [df['low'].iloc[i], df['LT'].iloc[i]], color='cyan')
            plt.plot([df.index[i], df.index[i]], [df['high'].iloc[i], df['HT'].iloc[i]], color='cyan')
        elif df['W1'].iloc[i] == -3:
            plt.plot([df.index[i], df.index[i]], [df['low'].iloc[i], df['LT'].iloc[i]], color='red')
            plt.plot([df.index[i], df.index[i]], [df['high'].iloc[i], df['HT'].iloc[i]], color='red')

        if df['W1'].iloc[i] == 1 and df['W1'].iloc[i - 1] == 1:
            plt.plot([df.index[i - 1], df.index[i]], [df['G'].iloc[i - 1], df['G'].iloc[i]], color='limegreen')
        elif df['W1'].iloc[i] == -3 and df['W1'].iloc[i - 1] == -3:
            plt.plot([df.index[i - 1], df.index[i]], [df['G'].iloc[i - 1], df['G'].iloc[i]], color='yellow')

    # 在最后一个点绘制G1的值
    plt.text(df.index[-1], df['G1'].iloc[-1], f"{df['G1'].iloc[-1]:.2f}", color='cyan', fontweight='bold')

    plt.title(symbol + name + ' ' + str(interval), color='white', fontweight='bold')
    plt.xlabel('Date', color='white')
    plt.ylabel('Price', color='white')
    plt.grid(True, color='gray', linestyle=':', alpha=0.5)

    # 调整x轴和y轴的颜色
    plt.tick_params(axis='x', colors='white')
    plt.tick_params(axis='y', colors='white')

    # 调整图例
    plt.legend(['MA1 Up', 'MA1 Down'], loc='upper left', facecolor='black', edgecolor='white')

    plt.tight_layout()
    plt.show()

    return


def get_positions():
    ret, data = trd_ctx.position_list_query()
    if ret == RET_OK:
        # print(data)
        if data.shape[0] > 0:  # 如果持仓列表不为空
            # print(data['stock_name'][0])  # 获取持仓第一个股票名称
            # print(data['stock_code'].values.tolist())
            return data['code'].values.tolist()
    else:
        print('position_list_query error: ', data)


#
positions = get_positions()
hk_position = [item for item in positions if 'HK' in item]
# us_position = [item for item in positions if 'US' in item]

symbollist = hk_position
knum = 1000  # 获取最近 1000 根 K 线数据

ret_sub, err_message = quote_ctx.subscribe(symbollist, [SubType.K_15M], subscribe_push=True)
# 先订阅 K 线类型。订阅成功后 OpenD 将持续收到服务器的推送，False 代表暂时不需要推送给脚本
if ret_sub == RET_OK:  # 订阅成功
    while True:
        # 检查是否在交易时间
        if not is_trading_time():
            current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            print(f"{current_time} 非交易时间，等待1分钟")
            time.sleep(60)
            continue

        positions = get_positions()
        hk_position = [item for item in positions if 'HK' in item]
        # us_position = [item for item in positions if 'US' in item]
        symbollist = hk_position

        for code in symbollist:
            ret, data = quote_ctx.get_cur_kline(code, knum, KLType.K_15M, AuType.NONE)  # 获取港股00700最近2个 K 线数据
            if ret == RET_OK:
                print(code, data.name.iloc[-1], data.close.iloc[-1], data.time_key.iloc[-1])
                df = wave_signal(data)

                # draw_wave_trading(df, knum=100)
                # rsi_info_display(data, code, data.name.iloc[-1], '15m')
                # print('*' * 50)
                # print(data['code'][0])    # 取第一条的股票代码
                # print(data['code'].values.tolist())   # 转为 list
                # print(data['turnover_rate'][0])   # 取第一条的换手率
                # print(data['turnover_rate'].values.tolist())   # 转为 list
            else:
                print('error:', data)
            time.sleep(0.0011)
        print(time.asctime(), '休息1分钟')
        time.sleep(60*5)  # 休眠5分钟，防止请求过于频繁
else:
    print('subscription failed', err_message)
quote_ctx.close()  # 关闭当条连接，OpenD 会在1分钟后自动取消相应股票相应类型的订阅
