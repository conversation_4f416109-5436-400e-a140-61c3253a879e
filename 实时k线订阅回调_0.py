import time
from futu import *


class CurKlineTest(CurKlineHandlerBase):
    def on_recv_rsp(self, rsp_pb):
        ret_code, data = super(CurKlineTest, self).on_recv_rsp(rsp_pb)
        if ret_code != RET_OK:
            print("CurKlineTest: error, msg: %s" % data)
            return RET_ERROR, data
        print("CurKlineTest ")
        print(data)# CurKlineTest's own processing logic
        return RET_OK, data


quote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)
handler = CurKlineTest()
quote_ctx.set_handler(handler)  # Set real-time candlestick callback
ret, data = quote_ctx.subscribe(['HK.00700'], [SubType.K_1M])  # Subscribe to the candlestick data type, OpenD starts to receive continuous push from the server
if ret == RET_OK:
    print(data)
else:
    print('error:', data)

while True:
    time.sleep(15)  # Set the script to receive OpenD push duration to 15 seconds

quote_ctx.close()  # Close the current link, OpenD will automatically cancel the corresponding type of subscription for the corresponding stock after 1 minute
