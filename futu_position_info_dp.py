from futu import TrdEnv, Market, OpenHKTradeContext, RET_OK

# 配置参数
host = "127.0.0.1"
port = 11111  # 模拟账户端口（需在FutuOpenD确认）
trd_env = TrdEnv.SIMULATE  # 模拟环境
trd_market = Market.HK  # 香港市场（关键修改！）

# 创建交易上下文
trd_ctx = OpenHKTradeContext(host=host, port=port)

# 获取期货持仓
ret, data = trd_ctx.position_list_query(
    trd_env=trd_env,
    # trd_market=Market.HK
    acc_id ='10046944' # 指定市场类型
)

if ret == RET_OK:
    print("期货持仓信息:")
    print(data[['code', 'name', 'qty', 'can_sell_qty', 'nominal_price']])
else:
    print("失败:", data)

trd_ctx.close()