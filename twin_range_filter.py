# This source code is subject to the terms of the Mozilla Public License 2.0 at https://mozilla.org/MPL/2.0/
# © colinmck

import numpy as np

def smoothrng(x, t, m):
    wper = t * 2 - 1
    avrng = np.exp_moving_avg(np.abs(x - np.roll(x, 1)), t)
    smoothrng = np.exp_moving_avg(avrng, wper) * m
    return smoothrng

def rngfilt(x, r):
    rngfilt = x.copy()
    rngfilt[x > np.roll(rngfilt, 1)] = np.minimum(np.roll(rngfilt, 1), x - r)
    rngfilt[x < np.roll(rngfilt, 1)] = np.maximum(np.roll(rngfilt, 1), x + r)
    return rngfilt


source = np.array(close)

per1 = 27
mult1 = 1.6
per2 = 55
mult2 = 2

smrng1 = smoothrng(source, per1, mult1)
smrng2 = smoothrng(source, per2, mult2)
smrng = (smrng1 + smrng2) / 2

filt = rngfilt(source, smrng)

upward = np.zeros_like(source)
upward[filt > np.roll(filt, 1)] = np.maximum(0, np.roll(upward, 1)[filt > np.roll(filt, 1)] + 1)
upward[filt < np.roll(filt, 1)] = 0

downward = np.zeros_like(source)
downward[filt < np.roll(filt, 1)] = np.maximum(0, np.roll(downward, 1)[filt < np.roll(filt, 1)] + 1)
downward[filt > np.roll(filt, 1)] = 0

hband = filt + smrng
lband = filt - smrng

longCond = np.full_like(source, False, dtype=bool)
shortCond = np.full_like(source, False, dtype=bool)
longCond[(source > filt) & (source > np.roll(source, 1)) & (upward > 0)] = True
longCond[(source > filt) & (source < np.roll(source, 1)) & (upward > 0)] = True
shortCond[(source < filt) & (source < np.roll(source, 1)) & (downward > 0)] = True
shortCond[(source < filt) & (source > np.roll(source, 1)) & (downward > 0)] = True

CondIni = np.zeros_like(source)
CondIni[longCond] = 1
CondIni[shortCond] = -1
CondIni[~(longCond | shortCond)] = np.roll(CondIni, 1)[~(longCond | shortCond)]

long = longCond & (np.roll(CondIni, 1) == -1)
short = shortCond & (np.roll(CondIni, 1) == 1)

