import futu as ft
import pandas as pd
import numpy as np
import time
from wavetrader_strategy import wavetrader  # Import the wavetrader function


class FutuTrader:
    def __init__(self,
                 host='127.0.0.1',
                 port=11111,
                 kline_type=ft.KLType.K_15M,
                 kline_num=100,
                 group_names=['港股', '沪深'],
                 check_interval=300):  # 默认5分钟检查一次
        """
        初始化交易系统

        Parameters:
        -----------
        host : str, optional
            富途牛牛API服务器地址，默认为本地
        port : int, optional
            富途牛牛API服务器端口，默认为11111
        kline_type : ft.KLType, optional
            K线类型，默认为15分钟
        kline_num : int, optional
            获取K线数量，默认为100
        group_names : list, optional
            自选股分组名称，默认为港股和沪深
        check_interval : int, optional
            检查间隔时间（秒），默认为300秒（5分钟）
        """
        # 初始化交易上下文
        self.quote_ctx = ft.OpenQuoteContext(host, port)

        # 参数设置
        self.kline_type = kline_type
        self.kline_num = kline_num
        self.group_names = group_names
        self.check_interval = check_interval

        # 账户信息
        self.cash_balance = 100000  # 初始资金

        # 持仓和订单记录
        self.positions = {}
        self.order_history = {}

        # 订阅股票列表
        self.symbol_list = self.get_optional_stocks()
        self.subscribe_stocks()

    def get_optional_stocks(self):
        """
        获取自选股列表

        Returns:
        --------
        list
            自选股代码列表
        """
        symbol_list = []

        for group_name in self.group_names:
            # 获取指定分组的股票
            ret, data = self.quote_ctx.get_user_security(group_name)

            if ret == ft.RET_OK and not data.empty:
                # 添加该分组的所有股票代码到symbol_list
                symbols = data['code'].values.tolist()
                symbol_list.extend(symbols)

                # 打印该分组的股票信息
                print(f"{group_name}分组股票:")
                for symbol in symbols:
                    print(symbol)
            else:
                print(f'获取{group_name}分组股票失败:', data)

        # 去重处理
        symbol_list = list(set(symbol_list))

        print("总自选股数量:", len(symbol_list))
        return symbol_list

    def subscribe_stocks(self):
        """
        订阅股票K线数据
        """
        # 订阅股票的K线数据
        ret_sub, err_message = self.quote_ctx.subscribe(
            self.symbol_list,
            self.kline_type,
            subscribe_push=True
        )

        if ret_sub != ft.RET_OK:
            print('股票订阅失败:', err_message)

    def get_stock_kline(self, stock_code):
        """
        获取股票最新K线数据

        Parameters:
        -----------
        stock_code : str
            股票代码

        Returns:
        --------
        pandas.DataFrame or None
            最新K线数据
        """
        # 获取最新K线数据
        ret, data = self.quote_ctx.get_cur_kline(
            stock_code,
            self.kline_num,
            self.kline_type,
            ft.AuType.NONE
        )

        if ret == ft.RET_OK:
            # 打印最新K线信息
            print(f"{stock_code} 最新K线: 时间={data.time_key.iloc[-1]}, 收盘价={data.close.iloc[-1]}")
            return data
        else:
            print(f'{stock_code} K线数据获取失败:', data)
            return None

    def trade_strategy(self, stock_code):
        """
        交易策略主函数

        Parameters:
        -----------
        stock_code : str
            股票代码
        """
        # 获取K线数据
        kline_data = self.get_stock_kline(stock_code)

        if kline_data is not None:
            # 应用WaveTrader策略
            strategy_result = wavetrader(kline_data)

            # 获取最小交易单位
            ret, lot_size_data = self.quote_ctx.get_stock_base_info(stock_code)
            if ret == ft.RET_OK:
                lot_size = lot_size_data['lot_size'].values[0]

                # 信号序列分析
                signals = strategy_result['SIGNAL_SEQUENCE']

                # 判断是否发出交易信号
                if len(signals) >= 2:
                    if signals[-2] == 1 and signals[-1] == -1:  # 前一个是卖出,后一个是买入
                        quantity = lot_size * 3  # 买入数量为最小交易单位的三倍
                        self.place_order(stock_code, 'BUY', quantity)
                        print(f"{stock_code} 发出买入信号")

                    elif signals[-2] == -1 and signals[-1] == 1:  # 前一个是买入,后一个是卖出
                        quantity = lot_size * 3  # 卖出数量为最小交易单位的三倍
                        self.place_order(stock_code, 'SELL', quantity)
                        print(f"{stock_code} 发出卖出信号")

    def run_trading(self):
        """
        运行交易系统
        """
        while True:
            # 遍历自选股执行交易策略
            for stock in self.symbol_list:
                try:
                    self.trade_strategy(stock)
                    time.sleep(0.5)  # 每个股票间隔0.5秒
                except Exception as e:
                    print(f"处理 {stock} 时发生错误: {e}")

            # 打印交易结果
            print("当前账户余额：", self.cash_balance)
            print("当前持仓：", self.positions)

            # 打印当前时间并等待下一次检查
            print(time.asctime(), f'休息{self.check_interval}秒')
            time.sleep(self.check_interval)

    def __del__(self):
        """
        关闭交易上下文
        """
        self.quote_ctx.close()


# 使用示例
if __name__ == "__main__":
    trader = FutuTrader(
        kline_type=ft.KLType.K_15M,  # 15分钟K线
        kline_num=100,  # 获取100个K线数据
        group_names=['港股'],  # 自选股分组
        check_interval=300  # 每5分钟检查一次
    )
    trader.run_trading()