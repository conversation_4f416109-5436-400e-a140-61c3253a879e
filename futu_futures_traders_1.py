from futu import *
import logging


class FuturesTrader:
    def __init__(self, host='127.0.0.1', port=11111):
        """
        初始化期货交易类

        :param host: Futu OpenD服务器地址，默认为本地
        :param port: Futu OpenD服务器端口，默认为11111
        """
        # 设置日志
        logging.basicConfig(level=logging.INFO,
                            format='%(asctime)s - %(levelname)s: %(message)s')
        self.logger = logging.getLogger(__name__)

        # 创建交易上下文
        self.trade_ctx = OpenFutureTradeContext(host=host, port=port)
        self.logger.info(f"期货交易上下文已创建：{host}:{port}")

    def open_long_position(self, code, qty, price=None):
        """
        开多仓

        :param code: 期货合约代码
        :param qty: 开仓数量
        :param price: 限价(可选)，不传则为市价
        :return: 交易结果
        """
        try:
            # 判断是市价还是限价
            if price is None:
                ret, data = self.trade_ctx.place_order(
                    code=code,
                    qty=qty,
                    trd_side=TrdSide.BUY,  # 使用通用的买入
                    order_type=OrderType.MARKET
                )
            else:
                ret, data = self.trade_ctx.place_order(
                    code=code,
                    qty=qty,
                    trd_side=TrdSide.BUY,  # 使用通用的买入
                    order_type=OrderType.NORMAL,
                    price=price
                )

            if ret == RET_OK:
                self.logger.info(f"成功开多仓 {code}, 数量: {qty}")
                return data
            else:
                self.logger.error(f"开多仓失败: {data}")
                return None

        except Exception as e:
            self.logger.error(f"开多仓异常: {e}")
            return None

    def open_short_position(self, code, qty, price=None):
        """
        开空仓

        :param code: 期货合约代码
        :param qty: 开仓数量
        :param price: 限价(可选)，不传则为市价
        :return: 交易结果
        """
        try:
            # 判断是市价还是限价
            if price is None:
                ret, data = self.trade_ctx.place_order(
                    code=code,
                    qty=qty,
                    trd_side=TrdSide.SELL,  # 使用通用的卖出
                    order_type=OrderType.MARKET
                )
            else:
                ret, data = self.trade_ctx.place_order(
                    code=code,
                    qty=qty,
                    trd_side=TrdSide.SELL,  # 使用通用的卖出
                    order_type=OrderType.NORMAL,
                    price=price
                )

            if ret == RET_OK:
                self.logger.info(f"成功开空仓 {code}, 数量: {qty}")
                return data
            else:
                self.logger.error(f"开空仓失败: {data}")
                return None

        except Exception as e:
            self.logger.error(f"开空仓异常: {e}")
            return None

    def close_long_position(self, code, qty, price=None):
        """
        平多仓

        :param code: 期货合约代码
        :param qty: 平仓数量
        :param price: 限价(可选)，不传则为市价
        :return: 交易结果
        """
        try:
            # 判断是市价还是限价
            if price is None:
                ret, data = self.trade_ctx.place_order(
                    code=code,
                    qty=qty,
                    trd_side=TrdSide.SELL,  # 平多仓实际上是卖出
                    order_type=OrderType.MARKET
                )
            else:
                ret, data = self.trade_ctx.place_order(
                    code=code,
                    qty=qty,
                    trd_side=TrdSide.SELL,  # 平多仓实际上是卖出
                    order_type=OrderType.NORMAL,
                    price=price
                )

            if ret == RET_OK:
                self.logger.info(f"成功平多仓 {code}, 数量: {qty}")
                return data
            else:
                self.logger.error(f"平多仓失败: {data}")
                return None

        except Exception as e:
            self.logger.error(f"平多仓异常: {e}")
            return None

    def close_short_position(self, code, qty, price=None):
        """
        平空仓

        :param code: 期货合约代码
        :param qty: 平仓数量
        :param price: 限价(可选)，不传则为市价
        :return: 交易结果
        """
        try:
            # 判断是市价还是限价
            if price is None:
                ret, data = self.trade_ctx.place_order(
                    code=code,
                    qty=qty,
                    trd_side=TrdSide.BUY,  # 平空仓实际上是买入
                    order_type=OrderType.MARKET
                )
            else:
                ret, data = self.trade_ctx.place_order(
                    code=code,
                    qty=qty,
                    trd_side=TrdSide.BUY,  # 平空仓实际上是买入
                    order_type=OrderType.NORMAL,
                    price=price
                )

            if ret == RET_OK:
                self.logger.info(f"成功平空仓 {code}, 数量: {qty}")
                return data
            else:
                self.logger.error(f"平空仓失败: {data}")
                return None

        except Exception as e:
            self.logger.error(f"平空仓异常: {e}")
            return None

    def __del__(self):
        """
        关闭交易上下文
        """
        if hasattr(self, 'trade_ctx'):
            self.trade_ctx.close()
            self.logger.info("期货交易上下文已关闭")


# 使用示例
def main():
    # 创建期货交易实例
    trader = FuturesTrader()

    # 期货合约代码（示例）
    # futures_code = 'HK.MHI'  # 恒生指数期货
    futures_code = 'HK.HSImain'  # 恒生指数期货

    # 市价开多仓示例
    # trader.open_long_position(futures_code, qty=1)

    # 限价开多仓示例
    trader.open_long_position(futures_code, qty=1, price=20000)

    # 市价平多仓示例
    # trader.close_long_position(futures_code, qty=1)


if __name__ == "__main__":
    main()