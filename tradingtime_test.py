from futu import *
import time
from datetime import datetime, time as datetime_time  # 重命名导入的time为datetime_time


def is_trading_time():
    """判断是否为交易时间"""
    current_time = datetime.now().time()
    morning_start = datetime_time(9, 30)  # 使用datetime_time而不是time
    morning_end = datetime_time(12, 0)
    afternoon_start = datetime_time(13, 0)
    afternoon_end = datetime_time(16, 0)

    # 判断是否在交易时间内
    is_morning_session = morning_start <= current_time <= morning_end
    is_afternoon_session = afternoon_start <= current_time <= afternoon_end

    return is_morning_session or is_afternoon_session


def main():
    """主函数"""
    quote_ctx = None
    try:
        quote_ctx = run_quote_connection()

        while True:
            if is_trading_time():
                print(f"交易时间，程序运行中... {datetime.now()}")
                time.sleep(1)
            else:
                print(f"非交易时间，等待中... {datetime.now()}")
                time.sleep(60)

    except Exception as e:
        print(f"程序运行出错: {str(e)}")
    finally:
        if quote_ctx:
            quote_ctx.close()