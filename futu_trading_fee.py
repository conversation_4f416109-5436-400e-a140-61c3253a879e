import math
from futu import *

def get_hk_stock_price(code):
    quote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)
    ret, data = quote_ctx.get_market_snapshot([code])
    quote_ctx.close()
    if ret == RET_OK and not data.empty:
        return data['last_price'].iloc[0]
    else:
        print('获取价格失败:', data)
        return None

def calc_futu_hk_trade_fee(price, qty):
    amount = price * qty
    commission = max(amount * 0.0003, 15)  # 佣金：0.03%，最低15港元
    platform_fee = 15                     # 平台使用费：15港元/笔
    stamp_duty = max(int(amount * 0.0013 + 0.9999), 1)  # 印花税：0.13%，最少1港元，向上取整
    trading_fee = round(amount * 0.00005, 3)            # 交易费：0.005%
    trading_sys_fee = round(amount * 0.00003, 3)        # 交易系统使用费：0.003%
    transaction_levy = round(amount * 0.00002, 3)       # 经手费：0.002%
    total_fee = commission + platform_fee + stamp_duty + trading_fee + trading_sys_fee + transaction_levy
    return {
        '成交金额': round(amount, 3),
        '佣金': round(commission, 3),
        '平台使用费': platform_fee,
        '印花税': stamp_duty,
        '交易费': trading_fee,
        '交易系统使用费': trading_sys_fee,
        '经手费': transaction_levy,
        '总费用': round(total_fee, 3)
    }

def main():
    code_input = input('请输入港股代码（如07226）：').strip()
    qty = int(input('请输入购买数量：').strip())
    code = f'HK.{code_input.zfill(5)}'
    price = get_hk_stock_price(code)
    if price is None:
        return
    fee_info = calc_futu_hk_trade_fee(price, qty)
    print(f"\n当前价格: {price:.3f} 港元")
    for k, v in fee_info.items():
        print(f"{k}: {v:.3f}")

if __name__ == '__main__':
    main()