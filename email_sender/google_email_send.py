#ofbx zdcc ahaj euss  pythonemail
#envi oriy kldp lduy  emailsend

import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart


def send_email(sender_email, app_password, recipient_email, subject, body):
    # 创建消息对象
    message = MIMEMultipart()
    message['From'] = sender_email
    message['To'] = recipient_email
    message['Subject'] = subject

    # 添加邮件正文
    message.attach(MIMEText(body, 'plain'))

    try:
        # 创建SMTP会话
        with smtplib.SMTP('smtp.gmail.com', 587) as server:
            # 开启TLS加密
            server.starttls()

            # 使用应用专用密码登录
            server.login(sender_email, app_password)

            # 发送邮件
            server.send_message(message)

        print("邮件发送成功！")
    except Exception as e:
        print(f"发送邮件时出错: {e}")


# 使用示例
if __name__ == '__main__':
    from google_email_conf import sender_email, app_password
    recipient_email = "<EMAIL>"  # 替换为收件人的邮箱地址
    subject = "测试邮件"
    body = "这是一封使用Python通过Gmail SMTP发送的测试邮件。按othertest"

    send_email(sender_email, app_password, recipient_email, subject, body)
