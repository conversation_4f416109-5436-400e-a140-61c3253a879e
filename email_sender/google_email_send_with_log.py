import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
import logging
from datetime import datetime
try:
    from .google_email_conf import sender_email,app_password
except:
    from google_email_conf import sender_email,app_password

subject = "交易信号提醒"
# 配置日志
logging.basicConfig(filename='email_log.txt', level=logging.INFO,encoding='UTF-8',
                    format='%(asctime)s - %(levelname)s - %(message)s')

logging.info('程序开始运行')
def mailsender(body, recipient_email):
    # 创建消息对象
    message = MIMEMultipart()
    message['From'] = sender_email
    message['To'] = recipient_email
    message['Subject'] = subject

    # 添加邮件正文
    message.attach(MIMEText(body, 'plain'))

    try:
        # 创建SMTP会话
        with smtplib.SMTP('smtp.gmail.com', 587) as server:
            # 开启TLS加密
            server.starttls()

            # 使用应用专用密码登录
            server.login(sender_email, app_password)

            # 发送邮件
            server.send_message(message)

        print("邮件发送成功！")
        logging.info(f"邮件成功发送给 {recipient_email}")
    except Exception as e:
        error_message = f"发送邮件给 {recipient_email} 时出错: {str(e)}"
        print(error_message)
        logging.error(error_message)


# 使用示例
if __name__ == '__main__':

    recipient_email = "<EMAIL>"  # 替换为收件人的邮箱地址
    subject = f"{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}测试邮件with log"
    body = "这是一封使用Python通过Gmail SMTP发送的测试邮件。"

    mailsender(recipient_email=recipient_email, body=body)