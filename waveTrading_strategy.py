import numpy as np
import pandas as pd


class WaveTradingStrategy:
    def __init__(self):
        pass

    @staticmethod
    def count_same_from_right(arr):
        if not arr:
            return 0
        last_element = arr[-1]
        count = 0
        for i in range(len(arr) - 1, -1, -1):
            if arr[i] == last_element:
                count += 1
            else:
                break
        return count

    @staticmethod
    def MA(series, n):
        return series.rolling(window=n).mean()

    @staticmethod
    def VALUEWHEN(condition, value):
        return value[condition].reindex(value.index).ffill()

    def wave_trading(self, df):
        df['MA1'] = self.MA(df['close'], 13)
        df['HH1'] = np.where((df['high'] < df['high'].shift(1)) & (df['high'].shift(1) < df['high'].shift(2)),
                             df['high'].shift(2), 0)
        df['HH2'] = self.VALUEWHEN(df['HH1'] > 0, df['HH1'])

        df['LL1'] = np.where((df['low'] > df['low'].shift(1)) & (df['low'].shift(1) > df['low'].shift(2)), df['low'].shift(2),
                             0)
        df['LL2'] = self.VALUEWHEN(df['LL1'] > 0, df['LL1'])

        df['K1'] = np.where(df['close'] > df['HH2'], -1, np.where(df['close'] < df['LL2'], 1, 0))
        df['K2'] = self.VALUEWHEN(df['K1'] != 0, df['K1'])

        df['G'] = np.where(df['K2'] == 1, df['HH2'], df['LL2'])
        df['G1'] = df['G'].iloc[-1]

        df['W1'] = df['K2']
        df['W2'] = df['open'] - df['close']
        df['HT'] = np.where(df['open'] > df['close'], df['open'], df['close'])
        df['LT'] = np.where(df['open'] < df['close'], df['open'], df['close'])
        df['signal'] = np.where(df['W1'] == 1, '空', '多')

        return df

    def wave_signal(self, df):
        strategy_name = '突破火线'
        symbol = df.code.iloc[-1]
        symbol_name = df.name.iloc[-1]
        df = self.wave_trading(df)

        signals = df.signal.tolist()
        signal_last = self.count_same_from_right(signals)
        signal_price = df.close.iloc[-signal_last]
        current_price = int(df.close.iloc[-1])
        current_signal = signals[-1]

        pnl = current_price - signal_price if current_signal == '多' else signal_price - current_price

        result = {
            'symbol': symbol,
            'symbol_name': symbol_name,
            'current_signal': current_signal,
            'current_price': current_price,
            'signal_price': signal_price,
            'signal_last': signal_last,
            'pnl': pnl,
            'continuous_periods': self.count_same_from_right(signals),
            'recent_signals': signals[-35:]
        }

        return result

    def print_results(self, result):
        print(f"{result['symbol']}_{result['symbol_name']}: current_signal:{result['current_signal']}, "
              f"Current price: {result['current_price']}, signal_price: {result['signal_price']}, "
              f"signal_last: {result['signal_last']}, pnl: {result['pnl']}")
        print(f"持续周期:{result['continuous_periods']}")
        print(result['recent_signals'])

    def generate_signal(self, df):
        result = self.wave_signal(df)
        self.print_results(result)

        if result['recent_signals'][-2] == '多' and result['recent_signals'][-1] == '空':
            return f"{result['symbol_name']}突破火线 发出做空信号"
        elif result['recent_signals'][-2] == '空' and result['recent_signals'][-1] == '多':
            return f"{result['symbol_name']}突破火线 发出做多信号"
        else:
            return None

# Usage example:
# strategy = WaveTradingStrategy()
# df = ... # Your DataFrame here
# signal = strategy.generate_signal(df)
# if signal:
#     speak_text(signal)  # Assuming speak_text function is defined elsewhere