from futu import *
import time
from rsi_strategies import rsi_info_display
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from futu_api_trader_class import FutuTrader
trader = FutuTrader()

plt.rcParams['font.family'] = ['sans-serif']
plt.rcParams['font.sans-serif'] = ['SimHei']
quote_ctx = OpenQuoteContext(host='**********', port=11111)

symbollist = []
interval = KLType.K_1M

def MA(series, n):
    return series.rolling(window=n).mean()


def VALUEWHEN(condition, value):
    return value[condition].reindex(value.index).ffill()


def wave_trading(df):
    # 计算指标

    df['MA1'] = MA(df['close'], 13)
    df['HH1'] = np.where((df['high'] < df['high'].shift(1)) & (df['high'].shift(1) < df['high'].shift(2)),
                         df['high'].shift(2), 0)
    df['HH2'] = VALUEWHEN(df['HH1'] > 0, df['HH1'])

    df['LL1'] = np.where((df['low'] > df['low'].shift(1)) & (df['low'].shift(1) > df['low'].shift(2)), df['low'].shift(2),
                         0)
    df['LL2'] = VALUEWHEN(df['LL1'] > 0, df['LL1'])

    df['K1'] = np.where(df['close'] > df['HH2'], -3, np.where(df['close'] < df['LL2'], 1, 0))
    df['K2'] = VALUEWHEN(df['K1'] != 0, df['K1'])

    df['G'] = np.where(df['K2'] == 1, df['HH2'], df['LL2'])
    df['G1'] = df['G'].iloc[-1]

    df['W1'] = df['K2']
    df['W2'] = df['open'] - df['close']
    df['HT'] = np.where(df['open'] > df['close'], df['open'], df['close'])
    df['LT'] = np.where(df['open'] < df['close'], df['open'], df['close'])

    return df


def draw_wave_trading(df,knum):
    symbol = df.code.iloc[0]
    name = df.name.iloc[0]
    # 仅保留最新knum条数据
    df = df.iloc[-knum:]

    # 设置绘图风格
    plt.style.use('dark_background')
    fig, ax = plt.subplots(figsize=(15, 10))


    # 绘制K线图

    def plot_candlestick(ax, df):


        width = 0.6
        width2 = 0.05

        up = df[df.close > df.open]
        down = df[df.close < df.open]
        equal = df[df.close == df.open]

        # 上涨K线 - 现在是红色
        ax.bar(up.index, up.close - up.open, width, bottom=up.open, color='red')
        ax.vlines(up.index, up.low, up.high, color='red', linewidth=1)

        # 下跌K线 - 现在是绿色
        ax.bar(down.index, down.close - down.open, width, bottom=down.open, color='green')
        ax.vlines(down.index, down.low, down.high, color='green', linewidth=1)

        # 开盘价等于收盘价的K线
        ax.bar(equal.index, width2, width, bottom=equal.open - width2 / 2, color='cyan')
        ax.vlines(equal.index, equal.low, equal.high, color='cyan', linewidth=1)


    plot_candlestick(ax, df)


    # 绘制MA1线
    # df['MA1'] = MA(df['close'], 13)
    ma1_up = np.where(df['MA1'] > df['MA1'].shift(1), df['MA1'], np.nan)
    ma1_down = np.where(df['MA1'] <= df['MA1'].shift(1), df['MA1'], np.nan)

    plt.plot(df.index, ma1_up, color='yellow', linewidth=4)
    plt.plot(df.index, ma1_down, color='cyan', linewidth=4)

    # 绘制其他线条
    for i in range(len(df) - 1):
        if df['W1'].iloc[i] == 1:
            plt.plot([df.index[i], df.index[i]], [df['low'].iloc[i], df['LT'].iloc[i]], color='cyan')
            plt.plot([df.index[i], df.index[i]], [df['high'].iloc[i], df['HT'].iloc[i]], color='cyan')
        elif df['W1'].iloc[i] == -3:
            plt.plot([df.index[i], df.index[i]], [df['low'].iloc[i], df['LT'].iloc[i]], color='red')
            plt.plot([df.index[i], df.index[i]], [df['high'].iloc[i], df['HT'].iloc[i]], color='red')

        if df['W1'].iloc[i] == 1 and df['W1'].iloc[i - 1] == 1:
            plt.plot([df.index[i - 1], df.index[i]], [df['G'].iloc[i - 1], df['G'].iloc[i]], color='limegreen')
        elif df['W1'].iloc[i] == -3 and df['W1'].iloc[i - 1] == -3:
            plt.plot([df.index[i - 1], df.index[i]], [df['G'].iloc[i - 1], df['G'].iloc[i]], color='yellow')

    # 在最后一个点绘制G1的值
    plt.text(df.index[-1], df['G1'].iloc[-1], f"{df['G1'].iloc[-1]:.2f}", color='cyan', fontweight='bold')

    plt.title(symbol + name+ ' '+ str(interval), color='white', fontweight='bold')
    plt.xlabel('Date', color='white')
    plt.ylabel('Price', color='white')
    plt.grid(True, color='gray', linestyle=':', alpha=0.5)

    # 调整x轴和y轴的颜色
    plt.tick_params(axis='x', colors='white')
    plt.tick_params(axis='y', colors='white')

    # 调整图例
    plt.legend(['MA1 Up', 'MA1 Down'], loc='upper left', facecolor='black', edgecolor='white')

    plt.tight_layout()
    plt.show()

    return




def get_user_security_by_grouname(groupname):
    ret, data = quote_ctx.get_user_security(groupname)

    if ret == RET_OK:
        # print(data)
        if data.shape[0] > 0:  # 如果自选股列表不为空
            print(data['code'][0])  # 取第一条的股票代码
            print(data['code'].values.tolist())  # 转为 list
    else:
        print('error:', data)

    return data['code'].values.tolist()


groupnames = ['港股', '沪深']
for groupname in groupnames:
    symbols = get_user_security_by_grouname(groupname)
    for item in symbols:
        symbollist.append(item)

knum = 1000  # 获取最近 1000 根 K 线数据
# symbol = ['HK.07552', 'HK.01772', 'HK.07226', 'HK.02898', 'HK.00020', 'HK.01810', 'HK.03690', 'HK.02007', 'HK.09988', 'HK.00358', 'HK.00700', 'HK.06055', 'HK.00788', 'HK.00005', 'HK.06862', 'HK.02318', 'HK.00175', 'HK.800000']

ret_sub, err_message = quote_ctx.subscribe(symbollist, [interval], subscribe_push=True)
# 先订阅 K 线类型。订阅成功后 OpenD 将持续收到服务器的推送，False 代表暂时不需要推送给脚本
if ret_sub == RET_OK:  # 订阅成功
    while True:
        for code in symbollist:
            ret, data = quote_ctx.get_cur_kline(code, knum, interval, AuType.NONE)  # 获取港股00700最近2个 K 线数据
            if ret == RET_OK:
                print(code,  data.name.iloc[-1],data.close.iloc[-1], data.time_key.iloc[-1])
                df = wave_trading(data)
                # draw_wave_trading(df, knum=100)
                rsi_info_display(data, code, data.name.iloc[-1], str(interval))
                # print(data['code'][0])    # 取第一条的股票代码
                # print(data['code'].values.tolist())   # 转为 list
                # print(data['turnover_rate'][0])   # 取第一条的换手率
                # print(data['turnover_rate'].values.tolist())   # 转为 list
            else:
                print('error:', data)
            time.sleep(0.01)
        print(time.asctime(), '休息1分钟')
        time.sleep(5 * 60)  # 休眠5分钟，防止请求过于频繁
else:
    print('subscription failed', err_message)
quote_ctx.close()  # 关闭当条连接，OpenD 会在1分钟后自动取消相应股票相应类型的订阅
