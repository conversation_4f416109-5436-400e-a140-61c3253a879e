import json
from futu import *
import time


def load_stock_data(json_file):
    """加载JSON文件中的股票数据"""
    with open(json_file, 'r', encoding='utf-8') as f:
        data = json.load(f)
    return data


def get_all_stock_codes(data):
    """从JSON数据中提取所有不重复的股票代码"""
    stock_codes = set()
    for user in data['users']:
        for code in user['stocks'].keys():
            # 转换股票代码格式为富途格式
            market, code_num = code.split('.')
            if market == 'SH':
                futu_code = f'SH.{code_num}'
            elif market == 'SZ':
                futu_code = f'SZ.{code_num}'
            elif market == 'HK':
                futu_code = f'HK.{code_num}'
            stock_codes.add(futu_code)
    return list(stock_codes)


def get_all_stock_codes_1(data):
    """从JSON数据中提取所有不重复的股票代码"""
    stock_codes = set()
    for code in data['stocks']:
        market, code_num = code.split('.')
        if market == 'SH':
            futu_code = f'SH.{code_num}'
        elif market == 'SZ':
            futu_code = f'SZ.{code_num}'
        elif market == 'HK':
            futu_code = f'HK.{code_num}'
        stock_codes.add(futu_code)
    return list(stock_codes)


def check_group_exists(quote_ctx, group_name):
    """检查自选股分组是否存在"""
    ret, data = quote_ctx.get_user_security_group()
    if ret != RET_OK:
        print(f"获取自选股分组失败: {data}")
        return False, None

    # 检查分组是否存在
    for _, row in data.iterrows():  # 使用 iterrows 遍历 DataFrame
        if row['group_name'] == group_name:  # 通过列名访问分组名称
            print(f"分组 '{group_name}' 已存在")
            return True  # 返回分组 ID
    print(f"分组 '{group_name}' 不存在")
    return False


def get_group_stock_list(quote_ctx, group_id):
    """获取分组中的股票列表"""
    ret, data = quote_ctx.get_user_security(group_id)
    if ret != RET_OK:
        print(f"获取分组股票列表失败: {data}")
        return []
    return data['code'].tolist()  # 返回股票代码列表


def create_group(quote_ctx, group_name):
    """创建新的自选股分组"""
    ret, data = quote_ctx.modify_user_security_group(
        op=ModifyUserSecurityGroupOp.ADD,
        group_name=group_name
    )
    if ret != RET_OK:
        print(f"创建分组 '{group_name}' 失败: {data}")
        return None
    print(f"分组 '{group_name}' 创建成功")
    return data['group_id']  # 返回新创建的分组 ID


def add_to_futu_watchlist(quote_ctx, stock_codes, group_name="friends_stocks"):
    """添加股票到富途自选股"""
    # 检查分组是否存在
    group_exists = check_group_exists(quote_ctx, group_name)

    if not group_exists:
        # 如果分组不存在，创建分组
        group_id = create_group(quote_ctx, group_name)
        if not group_id:
            print("无法创建分组，程序退出")
            return

    # 获取分组中的股票列表
    existing_stocks = get_group_stock_list(quote_ctx, group_name)
    if existing_stocks is None:
        print("无法获取分组股票列表，程序退出")
        return

    # 过滤掉已经存在的股票
    new_stocks = [code for code in stock_codes if code not in existing_stocks]
    print(new_stocks)
    if not new_stocks:
        print("所有股票已存在于分组中，无需添加")
        return

    # 分批添加股票
    batch_size = 3  # 每次添加的股票数量
    for i in range(0, len(new_stocks), batch_size):
        batch_codes = new_stocks[i:i + batch_size]
        try:
            ret, data = quote_ctx.modify_user_security(
                op=ModifyUserSecurityOp.ADD,  # 操作类型：添加
                code_list=batch_codes,        # 股票代码列表
                group_name=group_name         # 自选股分组名称
            )
            if ret != RET_OK:
                print(f"添加股票批次 {batch_codes} 失败: {data}")
            else:
                print(f"成功添加股票批次: {batch_codes}")
            time.sleep(3)  # 增加延时，避免触发频率限制
        except Exception as e:
            print(f"添加股票批次 {batch_codes} 时发生错误: {str(e)}")


def main():
    # 富途API配置
    host = 'localhost'  # 富途牛牛客户端IP
    port = 11111  # 富途牛牛客户端端口

    try:
        # 创建行情上下文
        quote_ctx = OpenQuoteContext(host=host, port=port)

        # 加载JSON数据
        data = load_stock_data('stocks_add_to_futu_chicang.json')  # 确保JSON文件路径正确
        print(data)

        # 获取所有股票代码
        stock_codes = get_all_stock_codes_1(data)
        print(stock_codes)
        print(f"总共需要添加 {len(stock_codes)} 只股票")

        # 添加到自选股
        add_to_futu_watchlist(quote_ctx, stock_codes, group_name="chicang")

    except Exception as e:
        print(f"发生错误: {str(e)}")

    finally:
        # 确保关闭连接
        if 'quote_ctx' in locals():
            quote_ctx.close()


if __name__ == "__main__":
    main()