from futu import *
import time
from datetime import datetime, time as datetime_time


class CurKlineTest(CurKlineHandlerBase):
    def on_recv_rsp(self, rsp_pb):
        """收到K线数据推送"""
        ret_code, data = super(CurKlineTest, self).on_recv_rsp(rsp_pb)
        if ret_code != RET_OK:
            print(f"CurKlineTest: error, msg: {data}")
            return RET_ERROR, data

        print(f"\n[{datetime.now()}] 收到K线数据更新:")
        if not data.empty:
            latest = data.iloc[-1]
            print(f"时间: {latest['time_key']}")
            print(f"开: {latest['open']:.2f} 高: {latest['high']:.2f} "
                  f"低: {latest['low']:.2f} 收: {latest['close']:.2f}")

            # 执行策略
            self.run_strategy(data)

        return RET_OK, data

    def run_strategy(self, kline_df):
        """策略函数"""
        try:
            if len(kline_df) >= 20:
                ma20 = kline_df['close'].rolling(20).mean()
                current_price = kline_df['close'].iloc[-1]
                ma20_value = ma20.iloc[-1]

                print(f"当前价格: {current_price:.2f}")
                print(f"20日均线: {ma20_value:.2f}")

                if current_price > ma20_value:
                    print("信号: 价格在20日均线上方")
                else:
                    print("信号: 价格在20日均线下方")

        except Exception as e:
            print(f"策略执行出错: {str(e)}")


def is_trading_time():
    """判断是否为交易时间"""
    current_time = datetime.now().time()
    morning_start = datetime_time(9, 30)
    morning_end = datetime_time(12, 0)
    afternoon_start = datetime_time(13, 0)
    afternoon_end = datetime_time(16, 0)

    is_morning_session = morning_start <= current_time <= morning_end
    is_afternoon_session = afternoon_start <= current_time <= afternoon_end

    return is_morning_session or is_afternoon_session


def main():
    """主函数"""
    quote_ctx = None
    try:
        # 创建行情对象
        quote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)

        # 创建并设置回调处理对象
        handler = CurKlineTest()
        quote_ctx.set_handler(handler)

        # 订阅K线数据
        stock_code = 'HK.00700'
        ret, data = quote_ctx.subscribe(stock_code, [SubType.K_1M])

        if ret == RET_OK:
            print('订阅成功:', data)

            # 获取初始K线数据
            ret, data = quote_ctx.get_cur_kline(
                code=stock_code,
                num=100,
                ktype=SubType.K_1M,
                autype=AuType.QFQ
            )

            if ret == RET_OK:
                print('\n初始K线数据:')
                print(data.tail())
                handler.run_strategy(data)

            # 持续运行，接收数据更新
            while True:
                if is_trading_time():
                    print(f"\r等待K线更新... {datetime.now()}", end='')
                    time.sleep(3)
                else:
                    print(f"\n非交易时间 {datetime.now()}")
                    time.sleep(60)

        else:
            print('订阅失败:', data)

    except Exception as e:
        print(f"程序运行出错: {str(e)}")
    finally:
        if quote_ctx:
            quote_ctx.close()


if __name__ == "__main__":
    main()
