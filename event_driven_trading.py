from futu import *
import time
import logging
import signal

class TradingBot:
    def __init__(self):
        self.quote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)
        self.trade_ctx = OpenHKTradeContext(host='127.0.0.1', port=11111)
        self.running = False
        self.setup_logger()

    def setup_logger(self):
        self.logger = logging.getLogger('TradingBot')
        self.logger.setLevel(logging.INFO)
        handler = logging.FileHandler('trading_bot.log')
        formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        handler.setFormatter(formatter)
        self.logger.addHandler(handler)

    def setup_connection(self):
        ret, data = self.quote_ctx.get_global_state()
        if ret != RET_OK:
            self.logger.error(f"Connection failed: {data}")
            return False
        self.logger.info("Connection established successfully")
        return True

    def setup_handlers(self):
        self.quote_ctx.set_handler(OrderBookUpdate<PERSON><PERSON>ler(self))
        self.quote_ctx.set_handler(TickerUpdateHandler(self))
        self.quote_ctx.set_handler(CurKlineHandler(self))

    def trading_strategy(self, data):
        if 'price' in data and len(data['price']) > 0:
            if data['price'][0] > data['price'].mean():
                self.place_order('buy', data['code'][0], 100)
            elif data['price'][0] < data['price'].mean():
                self.place_order('sell', data['code'][0], 100)
            self.logger.info(f"Strategy executed for {data['code'][0]}")
        else:
            self.logger.warning("Received data without price information")

    def place_order(self, direction, code, quantity):
        if direction == 'buy':
            ret, data = self.trade_ctx.place_order(price=0, qty=quantity, code=code, trd_side=TrdSide.BUY, order_type=OrderType.MARKET)
        elif direction == 'sell':
            ret, data = self.trade_ctx.place_order(price=0, qty=quantity, code=code, trd_side=TrdSide.SELL, order_type=OrderType.MARKET)
        if ret == RET_OK:
            self.logger.info(f"Placed {direction} order: {data}")
        else:
            self.logger.error(f"Failed to place {direction} order: {data}")

    def run(self):
        self.logger.info("Starting the trading bot...")
        ret, data = self.quote_ctx.subscribe(['HK.00700'], [SubType.TICKER, SubType.ORDER_BOOK, SubType.K_1M])
        if ret == RET_OK:
            self.logger.info(f"Subscribed successfully: {data}")
        else:
            self.logger.error(f"Subscription failed: {data}")
            return

        self.running = True
        while self.running:
            try:
                time.sleep(1)
            except KeyboardInterrupt:
                self.logger.info("Keyboard interrupt detected. Shutting down...")
                self.shutdown()

    def shutdown(self):
        self.logger.info("Shutting down the trading bot...")
        self.running = False
        self.quote_ctx.close()
        self.trade_ctx.close()
        self.logger.info("Trading bot shut down successfully.")

class OrderBookUpdateHandler(OrderBookHandlerBase):
    def __init__(self, bot):
        self.bot = bot

    def on_recv_rsp(self, rsp_str):
        ret_code, data = super(OrderBookUpdateHandler, self).on_recv_rsp(rsp_str)
        if ret_code != RET_OK:
            self.bot.logger.error(f"OrderBookUpdateHandler: error, msg: {data}")
            return RET_ERROR, data
        self.bot.logger.info(f"OrderBookUpdate: {data}")
        self.bot.trading_strategy(data)
        return RET_OK, data

class TickerUpdateHandler(TickerHandlerBase):
    def __init__(self, bot):
        self.bot = bot

    def on_recv_rsp(self, rsp_str):
        ret_code, data = super(TickerUpdateHandler, self).on_recv_rsp(rsp_str)
        if ret_code != RET_OK:
            self.bot.logger.error(f"TickerUpdateHandler: error, msg: {data}")
            return RET_ERROR, data
        self.bot.logger.info(f"TickerUpdate: {data}")
        self.bot.trading_strategy(data)
        return RET_OK, data

class CurKlineHandler(CurKlineHandlerBase):
    def __init__(self, bot):
        self.bot = bot

    def on_recv_rsp(self, rsp_str):
        ret_code, data = super(CurKlineHandler, self).on_recv_rsp(rsp_str)
        if ret_code != RET_OK:
            self.bot.logger.error(f"CurKlineHandler: error, msg: {data}")
            return RET_ERROR, data
        self.bot.logger.info(f"CurKlineUpdate: {data}")
        self.bot.trading_strategy(data)
        return RET_OK, data

if __name__ == "__main__":
    bot = TradingBot()
    if bot.setup_connection():
        bot.setup_handlers()

        def signal_handler(signum, frame):
            bot.logger.info("Received shutdown signal. Initiating graceful shutdown...")
            bot.shutdown()

        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)

        bot.run()