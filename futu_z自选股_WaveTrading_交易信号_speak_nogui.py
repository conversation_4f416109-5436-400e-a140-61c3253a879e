from futu import *
import time
from rsi_strategies import rsi_info_display
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from speaktext import speak_text
from loguru import logger as mylog
from email_sender.google_email_send_with_log import mailsender


mylog.add("futu_log.log", rotation="10 MB")
plt.rcParams['font.family'] = ['sans-serif']
plt.rcParams['font.sans-serif'] = ['SimHei']
server='**********'
quote_ctx = OpenQuoteContext(host=server, port=11111)



symbollist = []
interval = KLType.K_30M

def MA(series, n):
    return series.rolling(window=n).mean()


def VALUEWHEN(condition, value):
    return value[condition].reindex(value.index).ffill()


def wave_trading(df):
    # 计算指标

    df['MA1'] = MA(df['close'], 13)
    df['HH1'] = np.where((df['high'] < df['high'].shift(1)) & (df['high'].shift(1) < df['high'].shift(2)),
                         df['high'].shift(2), 0)
    df['HH2'] = VALUEWHEN(df['HH1'] > 0, df['HH1'])

    df['LL1'] = np.where((df['low'] > df['low'].shift(1)) & (df['low'].shift(1) > df['low'].shift(2)), df['low'].shift(2),
                         0)
    df['LL2'] = VALUEWHEN(df['LL1'] > 0, df['LL1'])

    df['K1'] = np.where(df['close'] > df['HH2'], -1, np.where(df['close'] < df['LL2'], 1, 0))
    df['K2'] = VALUEWHEN(df['K1'] != 0, df['K1'])

    df['G'] = np.where(df['K2'] == 1, df['HH2'], df['LL2'])
    df['G1'] = df['G'].iloc[-1]

    df['W1'] = df['K2']
    df['W2'] = df['open'] - df['close']
    df['HT'] = np.where(df['open'] > df['close'], df['open'], df['close'])
    df['LT'] = np.where(df['open'] < df['close'], df['open'], df['close'])

    return df




def get_user_security_by_grouname(groupname):
    ret, data = quote_ctx.get_user_security(groupname)

    if ret == RET_OK:
        # print(data)
        if data.shape[0] > 0:  # 如果自选股列表不为空
            print(data['code'][0])  # 取第一条的股票代码
            print(data['code'].values.tolist())  # 转为 list
    else:
        print('error:', data)

    return data['code'].values.tolist()


groupnames = ['港股', '沪深']
for groupname in groupnames:
    symbols = get_user_security_by_grouname(groupname)
    for item in symbols:
        symbollist.append(item)

knum = 1000  # 获取最近 1000 根 K 线数据
# symbol = ['HK.07552', 'HK.01772', 'HK.07226', 'HK.02898', 'HK.00020', 'HK.01810', 'HK.03690', 'HK.02007', 'HK.09988', 'HK.00358', 'HK.00700', 'HK.06055', 'HK.00788', 'HK.00005', 'HK.06862', 'HK.02318', 'HK.00175', 'HK.800000']

ret_sub, err_message = quote_ctx.subscribe(symbollist, interval, subscribe_push=True)
# 先订阅 K 线类型。订阅成功后 OpenD 将持续收到服务器的推送，False 代表暂时不需要推送给脚本
if ret_sub == RET_OK:  # 订阅成功
    while True:
        for code in symbollist:
            ret, data = quote_ctx.get_cur_kline(code, knum, interval, AuType.NONE)  # 获取港股00700最近2个 K 线数据
            if ret == RET_OK:
                stockname = data.name.iloc[-1]
                signal_price = data.close.iloc[-1]

                print(code,  stockname, signal_price, data.time_key.iloc[-1])

                df = wave_trading(data)
                current_signal = "做多" if df['W1'].iloc[-1] == 1 else "做空"
                print(df['W1'].to_list()[-20:])
                if df['W1'].iloc[-1] == -1 and df['W1'].iloc[-2] == 1:
                    print('买入信号')
                    signal_price = df.close.iloc[-1]
                    speak_text(stockname+'买入信号'+str(signal_price))
                    mylog.info(stockname+'买入信号'+str(signal_price))
                    recipient_email = "<EMAIL>"
                    body_text = f"{stockname}_买入信号:{signal_price}"
                    mailsender(recipient_email=recipient_email, body=body_text)

                    current_signal = "做多"


                elif df['W1'].iloc[-1] == 1 and df['W1'].iloc[-2] == -1:
                    print('卖出信号')
                    # speak_text('卖出信号')
                    current_signal = "做空"
                    signal_price = df.close.iloc[-1]
                    speak_text(stockname + '卖出信号' + str(signal_price))
                    mylog.info(stockname + '卖出信号' + str(signal_price))

                    recipient_email = "<EMAIL>"
                    body_text = f"{stockname}_卖出信号:{signal_price}"
                    mailsender(recipient_email=recipient_email, body=body_text)

                # rsi_info_display(data, code, data.name.iloc[-1], '15m')
                # print(data['code'][0])    # 取第一条的股票代码
                # print(data['code'].values.tolist())   # 转为 list
                # print(data['turnover_rate'][0])   # 取第一条的换手率
                # print(data['turnover_rate'].values.tolist())   # 转为 list
            else:
                print('error:', data)
            time.sleep(0.01)
        print(time.asctime(), '休息1分钟')
        time.sleep(15 * 60)  # 休眠5分钟，防止请求过于频繁
else:
    print('subscription failed', err_message)
quote_ctx.close()  # 关闭当条连接，OpenD 会在1分钟后自动取消相应股票相应类型的订阅
