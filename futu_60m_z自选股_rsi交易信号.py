from futu import *
import time
from rsi_strategies import rsi_info_display

quote_ctx = OpenQuoteContext(host='**********', port=11111)

symbollist = []


def get_user_security_by_grouname(groupname):
    ret, data = quote_ctx.get_user_security(groupname)

    if ret == RET_OK:
        # print(data)
        if data.shape[0] > 0:  # 如果自选股列表不为空
            print(data['code'][0])  # 取第一条的股票代码
            print(data['code'].values.tolist())  # 转为 list
    else:
        print('error:', data)

    return data['code'].values.tolist()


# groupnames = ['港股', '沪深']
groupnames = ['chicang']
for groupname in groupnames:
    symbols = get_user_security_by_grouname(groupname)
    for item in symbols:
        symbollist.append(item)

knum = 1000  # 获取最近 1000 根 K 线数据

ret_sub, err_message = quote_ctx.subscribe(symbollist, [SubType.K_15M], subscribe_push=True)
# 先订阅 K 线类型。订阅成功后 OpenD 将持续收到服务器的推送，False 代表暂时不需要推送给脚本
if ret_sub == RET_OK:  # 订阅成功
    while True:
        for code in symbollist:
            ret, data = quote_ctx.get_cur_kline(code, knum, KLType.K_15M, AuType.NONE)  # 获取港股00700最近2个 K 线数据
            if ret == RET_OK:
                print(code,  data.name.iloc[-1],data.close.iloc[-1], data.time_key.iloc[-1])
                rsi_info_display(data, code, data.name.iloc[-1], '15m')
                # print(data['code'][0])    # 取第一条的股票代码
                # print(data['code'].values.tolist())   # 转为 list
                # print(data['turnover_rate'][0])   # 取第一条的换手率
                # print(data['turnover_rate'].values.tolist())   # 转为 list
            else:
                print('error:', data)
            time.sleep(0.0011)
        print(time.asctime(), '休息1分钟')
        time.sleep(5 * 60)  # 休眠5分钟，防止请求过于频繁
else:
    print('subscription failed', err_message)
quote_ctx.close()  # 关闭当条连接，OpenD 会在1分钟后自动取消相应股票相应类型的订阅
