import futu as ft
from futu import *
import time
import pandas as pd
from datetime import datetime


class FuturesTradingSimulator:
    def __init__(self, host='127.0.0.1', port=11111):
        # 初始化Futu API连接（使用期货交易上下文）
        self.trade_context = OpenFutureTradeContext(
            host=host,
            port=port,
            is_encrypt=None,
            security_firm=SecurityFirm.FUTUSECURITIES
        )
        self.quote_context = OpenQuoteContext(host=host, port=port)

        # 交易参数
        self.symbol = "HK.HSImain"  # 恒生指数期货主力合约
        self.lot_size = 50  # 每手合约单位
        self.futures_acc_id = None  # 用于存储期货账户ID
        self.max_position = 10  # 最大持仓限制：10手
        self.entry_price = None  # 记录开仓价格
        self.timeframe = "K_5M"  # 时间周期
        self.log_file = None  # 日志文件名，待账户确定后设置

    def log(self, message):
        """带时间戳的日志输出并保存到文件"""
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        log_entry = f"[{timestamp}] [Account: {self.futures_acc_id or 'N/A'}] [Symbol: {self.symbol}] [Timeframe: {self.timeframe}] {message}"
        print(log_entry)  # 输出到控制台
        if self.log_file:  # 确保日志文件已初始化
            with open(self.log_file, 'a', encoding='utf-8') as f:
                f.write(log_entry + '\n')

    def connect(self):
        """建立连接并订阅行情"""
        ret, data = self.trade_context.get_acc_list()
        if ret == RET_OK:
            self.log('账户列表:')
            print(data)  # 账户列表保持多行输出
            for index, row in data.iterrows():
                trd_env = row['trd_env']
                trdmarket_auth = row['trdmarket_auth']
                if trd_env == 'SIMULATE' and 'FUTURES_SIMULATE_HK' in trdmarket_auth:
                    self.futures_acc_id = row['acc_id']
                    # 设置日志文件名
                    self.log_file = f"futures_trading_{self.futures_acc_id}_{self.symbol}_{self.timeframe}.txt"
                    self.log(f"找到香港期货模拟账户: {self.futures_acc_id}")
                    break
            if self.futures_acc_id is None:
                self.log("未找到支持香港期货模拟交易的账户，请检查账户权限")
                return False
        else:
            self.log(f"获取账户列表失败: {data}")
            return False

        ret, data = self.quote_context.subscribe(self.symbol, SubType.K_5M)
        if ret == RET_OK:
            self.log('5分钟K线行情订阅成功')
        else:
            self.log(f"行情订阅失败: {data}")
            return False
        return True

    def get_market_data(self):
        """获取实时市场数据（5分钟K线，初始300根）"""
        ret, data = self.quote_context.get_cur_kline(self.symbol, num=300, ktype=SubType.K_5M)
        if ret == RET_OK:
            return data
        else:
            self.log(f"获取行情失败: {data}")
            return None

    def get_account_info(self):
        """获取账户信息"""
        ret, data = self.trade_context.accinfo_query(trd_env=TrdEnv.SIMULATE, acc_id=self.futures_acc_id)
        if ret == RET_OK:
            return data
        else:
            self.log(f"获取账户信息失败: {data}")
            return None

    def place_order(self, price, qty, direction):
        """提交真实模拟订单"""
        if self.futures_acc_id is None:
            self.log("未找到期货账户，无法下单")
            return

        if direction == 'BUY':
            trd_side = TrdSide.BUY
        elif direction == 'SELL':
            trd_side = TrdSide.SELL
        else:
            self.log("无效的交易方向")
            return

        ret, data = self.trade_context.place_order(
            price=price,
            qty=qty,
            code=self.symbol,
            trd_side=trd_side,
            order_type=OrderType.NORMAL,
            trd_env=TrdEnv.SIMULATE,
            acc_id=self.futures_acc_id
        )

        if ret == RET_OK:
            self.log(f"{direction} 订单提交成功: {qty}手 @ {price}")
            self.log(f"订单详情: {data}")
            if direction == 'BUY':
                self.entry_price = price  # 记录开仓价格
        else:
            self.log(f"{direction} 订单提交失败: {data}")

    def simple_trading_strategy(self):
        """优化交易策略：基于13周期均线的趋势跟随（5分钟K线）"""
        market_data = self.get_market_data()
        if market_data is None or len(market_data) < 14:
            return

        # 计算13周期简单移动平均线
        ma13 = market_data['close'].rolling(window=13).mean()
        current_price = market_data['close'].iloc[-1]  # 当前价格
        prev_price = market_data['close'].iloc[-2]  # 前一周期价格
        current_ma13 = ma13.iloc[-1]  # 当前MA13
        prev_ma13 = ma13.iloc[-2]  # 前一周期MA13

        # 获取当前持仓
        ret, position_data = self.trade_context.position_list_query(
            code=self.symbol, trd_env=TrdEnv.SIMULATE, acc_id=self.futures_acc_id
        )
        current_position = 0
        if ret == RET_OK and not position_data.empty:
            current_position = int(position_data['qty'].iloc[0])

        # 交易逻辑
        # 上穿MA13：买入1手（持仓不超过10手）
        if prev_price <= prev_ma13 and current_price > current_ma13 and current_position < self.max_position:
            self.place_order(current_price, 1, 'BUY')

        # 下穿MA13且价格大于开仓价格：平仓
        elif prev_price >= prev_ma13 and current_price < current_ma13 and current_position > 0:
            if self.entry_price is not None and current_price > self.entry_price:
                self.place_order(current_price, current_position, 'SELL')
                self.entry_price = None  # 平仓后清空开仓价格

    def run(self):
        """运行模拟交易"""
        if not self.connect():
            return

        self.log("开始模拟交易...")
        try:
            while True:
                self.simple_trading_strategy()
                account_info = self.get_account_info()
                if account_info is not None:
                    self.log(f"当前资金: ${account_info['cash'].iloc[0]:.2f}")
                time.sleep(300)  # 每5分钟检查一次（300秒）
        except KeyboardInterrupt:
            self.log("交易模拟结束")
            self.quote_context.close()
            self.trade_context.close()


if __name__ == "__main__":
    simulator = FuturesTradingSimulator()
    simulator.run()