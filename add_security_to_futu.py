from futu import *
import time


def add_stocks_to_watchlist(stock_list, group_name="特别关注A"):
    """
    将股票添加到富途自选股列表中，如果分组不存在则创建

    Parameters:
    stock_list (list): 股票代码列表
    group_name (str): 自选股分组名称
    """
    # 连接 FutuOpenD
    quote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)

    try:
        # 获取所有自选股分组
        ret, data = quote_ctx.get_user_security_group()
        if ret != RET_OK:
            print(f'获取自选股分组失败: {data}')
            return

        # 查找目标分组ID
        target_group = None
        for group in data:
            if group['group_name'] == group_name:
                target_group = group
                break

        # 如果分组不存在，创建新分组
        if target_group is None:
            print(f'分组 {group_name} 不存在，正在创建...')
            ret, data = quote_ctx.create_user_security_group(group_name)
            if ret != RET_OK:
                print(f'创建分组失败: {data}')
                return

            # 重新获取分组信息以获取新创建的分组ID
            ret, data = quote_ctx.get_user_security_group()
            if ret != RET_OK:
                print(f'获取分组信息失败: {data}')
                return

            for group in data:
                if group['group_name'] == group_name:
                    target_group = group
                    break

            print(f'成功创建分组 {group_name}')

        group_id = target_group['group_id']

        # 添加股票到分组
        for stock in stock_list:
            # 转换为富途格式的股票代码
            if stock.endswith('.SZ'):
                futu_code = f'SZ.{stock[:6]}'
            elif stock.endswith('.BJ'):
                futu_code = f'BJ.{stock[:6]}'
            else:
                print(f'不支持的股票代码格式: {stock}')
                continue

            ret, data = quote_ctx.modify_user_security_group(
                group_id=group_id,
                op=SecurityGroupOp.ADD,
                code_list=[futu_code]
            )

            if ret == RET_OK:
                print(f'成功添加 {stock} ({futu_code}) 到 {group_name}')
            else:
                print(f'添加 {stock} 失败: {data}')

            # 添加延时避免请求过快
            time.sleep(0.5)

    except Exception as e:
        print(f'发生错误: {str(e)}')

    finally:
        # 关闭连接
        quote_ctx.close()


# 股票列表
stocks = ['000655.SZ', '000725.SZ', '000762.SZ', '000792.SZ',
          '002597.SZ', '300220.SZ', '300450.SZ', '400254.BJ']

# 执行添加操作
add_stocks_to_watchlist(stocks)