
from futu import *
import time
from rsi_strategies import rsi_info_display
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from speaktext import speak_text
from loguru import logger as mylog
from email_sender.google_email_send_with_log import mailsender

class WaveTrader:
    def __init__(self, server='localhost', port=11111, interval=KLType.K_15M, knum=1000):
        self.server = server
        self.port = port
        self.interval = interval
        self.knum = knum
        self.quote_ctx = OpenQuoteContext(host=server, port=port)
        self.symbollist = []
        mylog.add("futu_log.log", rotation="10 MB")
        plt.rcParams['font.family'] = ['sans-serif']
        plt.rcParams['font.sans-serif'] = ['SimHei']

    def MA(self, series, n):
        return series.rolling(window=n).mean()

    def VALUEWHEN(self, condition, value):
        return value[condition].reindex(value.index).ffill()

    def wave_trading(self, df):
        df['MA1'] = self.MA(df['close'], 13)
        df['HH1'] = np.where((df['high'] < df['high'].shift(1)) & (df['high'].shift(1) < df['high'].shift(2)),
                             df['high'].shift(2), 0)
        df['HH2'] = self.VALUEWHEN(df['HH1'] > 0, df['HH1'])
        df['LL1'] = np.where((df['low'] > df['low'].shift(1)) & (df['low'].shift(1) > df['low'].shift(2)), df['low'].shift(2),
                             0)
        df['LL2'] = self.VALUEWHEN(df['LL1'] > 0, df['LL1'])
        df['K1'] = np.where(df['close'] > df['HH2'], -1, np.where(df['close'] < df['LL2'], 1, 0))
        df['K2'] = self.VALUEWHEN(df['K1'] != 0, df['K1'])
        df['G'] = np.where(df['K2'] == 1, df['HH2'], df['LL2'])
        df['G1'] = df['G'].iloc[-1]
        df['W1'] = df['K2']
        df['W2'] = df['open'] - df['close']
        df['HT'] = np.where(df['open'] > df['close'], df['open'], df['close'])
        df['LT'] = np.where(df['open'] < df['close'], df['open'], df['close'])
        return df

    def get_user_security_by_groupname(self, groupname):
        ret, data = self.quote_ctx.get_user_security(groupname)
        if ret == RET_OK:
            return data['code'].values.tolist()
        else:
            print('error:', data)
            return []

    def subscribe_symbols(self, symbollist):
        ret_sub, err_message = self.quote_ctx.subscribe(symbollist, [SubType.K_15M], subscribe_push=True)
        if ret_sub != RET_OK:
            print('subscription failed', err_message)
        return ret_sub == RET_OK

    def process_signals(self, symbollist):
        while True:
            for code in symbollist:
                ret, data = self.quote_ctx.get_cur_kline(code, self.knum, KLType.K_15M, AuType.NONE)
                if ret == RET_OK:
                    stockname = data.name.iloc[-1]
                    signal_price = data.close.iloc[-1]
                    df = self.wave_trading(data)
                    current_signal = "做多" if df['W1'].iloc[-1] == 1 else "做空"
                    print(df['W1'].to_list()[-20:])
                    if df['W1'].iloc[-1] == -1 and df['W1'].iloc[-2] == 1:
                        self.handle_signal(stockname, signal_price, '买入信号', '做多')
                    elif df['W1'].iloc[-1] == 1 and df['W1'].iloc[-2] == -1:
                        self.handle_signal(stockname, signal_price, '卖出信号', '做空')
                else:
                    print('error:', data)
                time.sleep(0.01)
            print(time.asctime(), '休息1分钟')
            time.sleep(15 * 60)

    def handle_signal(self, stockname, signal_price, signal_text, current_signal):
        print(signal_text)
        speak_text(f"{stockname} {signal_text} {signal_price}")
        mylog.info(f"{stockname} {signal_text} {signal_price}")
        recipient_email = "<EMAIL>"
        body_text = f"{stockname}_{signal_text}:{signal_price}"
        mailsender(recipient_email=recipient_email, body=body_text)

    def run(self, groupnames):
        for groupname in groupnames:
            symbols = self.get_user_security_by_groupname(groupname)
            self.symbollist.extend(symbols)
        if self.subscribe_symbols(self.symbollist):
            self.process_signals(self.symbollist)
        self.quote_ctx.close()

if __name__ == "__main__":
    trader = WaveTrader()
    groupnames = ['friends_stocks']
    trader.run(groupnames)