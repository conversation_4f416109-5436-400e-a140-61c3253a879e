# 明确导入所需的类
from futu import (
    OpenQuoteContext,
    OpenHKTradeContext,  # 修改：使用港股交易接口
    KLCallback,
    TrdSide,
    OrderType,
    TrdEnv,
    SubType,
    KLType,
    RET_OK,
    RET_ERROR
)
import pandas as pd
import numpy as np
import time
from datetime import datetime


class StrategyHandler(KLCallback):
    def __init__(self):
        super().__init__()
        self.kline_df = pd.DataFrame()
        self.ma_short = 5
        self.ma_long = 20
        self.last_signal = None
        self.trade_ctx = None

    def setup_trade_context(self, host='127.0.0.1', port=11111):
        """设置交易上下文"""
        # 修改：根据交易市场选择适当的交易接口
        self.trade_ctx = OpenHKTradeContext(host=host, port=port)  # 港股交易接口
        # 如果是A股，使用：
        # self.trade_ctx = OpenCNTradeContext(host=host, port=port)
        # 如果是美股，使用：
        # self.trade_ctx = OpenUSTradeContext(host=host, port=port)

    def calculate_signals(self, df):
        """计算交易信号"""
        if len(df) < self.ma_long:
            return None

        # 计算移动平均线
        df['MA_short'] = df['close'].rolling(window=self.ma_short).mean()
        df['MA_long'] = df['close'].rolling(window=self.ma_long).mean()

        # 计算金叉死叉
        df['cross_over'] = (df['MA_short'] > df['MA_long']) & (df['MA_short'].shift(1) <= df['MA_long'].shift(1))
        df['cross_under'] = (df['MA_short'] < df['MA_long']) & (df['MA_short'].shift(1) >= df['MA_long'].shift(1))

        # 获取最新的信号
        latest = df.iloc[-1]
        signal = None

        if latest['cross_over']:
            signal = 'BUY'
        elif latest['cross_under']:
            signal = 'SELL'

        return signal

    def execute_trade(self, signal, stock_code):
        """执行交易"""
        if self.trade_ctx is None or signal == self.last_signal:
            return

        if signal == 'BUY':
            # 执行买入操作
            ret, data = self.trade_ctx.place_order(
                price=0,  # 市价单
                qty=100,  # 买入数量
                code=stock_code,
                trd_side=TrdSide.BUY,
                order_type=OrderType.MARKET,
                trd_env=TrdEnv.REAL  # 实盘交易
            )
            if ret == RET_OK:
                print(f"下单成功: 买入 {stock_code}")
            else:
                print(f"下单失败: {data}")

        elif signal == 'SELL':
            # 执行卖出操作
            ret, data = self.trade_ctx.place_order(
                price=0,
                qty=100,
                code=stock_code,
                trd_side=TrdSide.SELL,
                order_type=OrderType.MARKET,
                trd_env=TrdEnv.REAL
            )
            if ret == RET_OK:
                print(f"下单成功: 卖出 {stock_code}")
            else:
                print(f"下单失败: {data}")

        self.last_signal = signal

    def on_recv_rsp(self, rsp_pb):
        """K线数据回调处理"""
        ret_code = rsp_pb.ret_code
        if ret_code != RET_OK:
            print(f"K线数据接收异常: {rsp_pb.ret_code} {rsp_pb.ret_msg}")
            return RET_ERROR

        k_line = rsp_pb.k_line
        if k_line is None or len(k_line) == 0:
            return RET_OK

        # 将新K线数据转换为DataFrame
        new_data = []
        for kline_data in k_line:
            new_data.append({
                'time_key': kline_data.time_key,
                'open': kline_data.open,
                'high': kline_data.high,
                'low': kline_data.low,
                'close': kline_data.close,
                'volume': kline_data.volume,
                'turnover': kline_data.turnover
            })

        new_df = pd.DataFrame(new_data)

        # 更新K线数据
        self.kline_df = pd.concat([self.kline_df, new_df]).drop_duplicates(subset=['time_key'])
        self.kline_df = self.kline_df.sort_values('time_key').reset_index(drop=True)

        # 保留最近1000根K线用于计算
        if len(self.kline_df) > 1000:
            self.kline_df = self.kline_df.tail(1000)

        # 计算策略信号
        signal = self.calculate_signals(self.kline_df)

        if signal:
            print(f"产生信号: {signal} at {datetime.now()}")
            # 执行交易
            self.execute_trade(signal, rsp_pb.code)

        return RET_OK


def run_strategy():
    # 连接行情上下文
    quote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)

    # 创建策略处理器
    handler = StrategyHandler()

    # 设置交易上下文（如果需要实盘交易）
    handler.setup_trade_context()

    # 设置回调处理对象
    quote_ctx.set_handler(handler)

    # 订阅K线数据
    stock_code = 'HK.00700'  # 以腾讯股票为例
    ret, data = quote_ctx.subscribe(
        stock_code,
        [SubType.K_1M],  # 订阅1分钟K线
        subscribe_push=True
    )

    if ret != RET_OK:
        print(f'订阅失败: {data}')
        quote_ctx.close()
        return

    print(f'成功订阅 {stock_code} 的K线数据')

    # 获取初始K线数据
    ret, data = quote_ctx.get_cur_kline(
        code=stock_code,
        num=100,  # 获取最近100根K线
        ktype=KLType.K_1M
    )

    if ret == RET_OK:
        handler.kline_df = data
        print('获取历史K线成功')
    else:
        print('获取历史K线失败:', data)

    try:
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("程序终止")
    finally:
        quote_ctx.close()
        if handler.trade_ctx:
            handler.trade_ctx.close()


if __name__ == "__main__":
    run_strategy()