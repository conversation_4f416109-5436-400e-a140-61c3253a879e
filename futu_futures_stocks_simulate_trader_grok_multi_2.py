import futu as ft
from futu import *
import time
import pandas as pd
from datetime import datetime


class SingleCategoryTradingSimulator:
    def __init__(self, host='127.0.0.1', port=11111, trade_type='futures', symbols=None):
        # 参数初始化
        self.trade_type = trade_type.lower()  # 'futures' 或 'stock'
        self.symbols = symbols if symbols is not None else []  # 交易标的数组
        self.timeframe = "K_5M"  # 时间周期
        self.max_position = 10  # 最大持仓限制
        self.entry_prices = {}  # 记录每个标的的开仓价格
        self.acc_id = None  # 账户ID
        self.log_file = None

        # 验证参数
        if self.trade_type not in ['futures', 'stock']:
            raise ValueError("trade_type must be 'futures' or 'stock'")
        if not self.symbols:
            raise ValueError("symbols array cannot be empty")

        # 初始化交易上下文
        if self.trade_type == 'futures':
            self.trade_context = OpenFutureTradeContext(
                host=host, port=port, is_encrypt=None, security_firm=SecurityFirm.FUTUSECURITIES
            )
            self.lot_size = 50
            self.market_auth_key = 'FUTURES_SIMULATE_HK'
            self.unit = '手'
        else:  # stock
            self.trade_context = OpenHKTradeContext(
                host=host, port=port, is_encrypt=None, security_firm=SecurityFirm.FUTUSECURITIES
            )
            self.lot_size = 1
            self.market_auth_key = 'HK'
            self.unit = '股'

        self.quote_context = OpenQuoteContext(host=host, port=port)

    def log(self, message, symbol=None):
        """带时间戳的日志输出并保存到文件"""
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        symbol_info = f"[Symbol: {symbol}]" if symbol else ""
        log_entry = f"[{timestamp}] [Account: {self.acc_id or 'N/A'}] {symbol_info} [Timeframe: {self.timeframe}] {message}"
        print(log_entry)
        if self.log_file:
            with open(self.log_file, 'a', encoding='utf-8') as f:
                f.write(log_entry + '\n')

    def connect(self):
        """建立连接并订阅行情"""
        ret, data = self.trade_context.get_acc_list()
        if ret == RET_OK:
            self.log(f"{self.trade_type.capitalize()} 账户列表:")
            print(data)
            for index, row in data.iterrows():
                if row['trd_env'] == 'SIMULATE' and self.market_auth_key in row['trdmarket_auth']:
                    self.acc_id = row['acc_id']
                    self.log_file = f"trading_{self.trade_type}_{self.acc_id}_{self.timeframe}.txt"
                    self.log(f"找到{self.trade_type}模拟账户: {self.acc_id}")
                    break
            if self.acc_id is None:
                self.log(f"未找到支持{self.trade_type}模拟交易的账户，请检查账户权限")
                return False
        else:
            self.log(f"获取账户列表失败: {data}")
            return False

        # 订阅所有标的行情
        for symbol in self.symbols:
            ret, data = self.quote_context.subscribe(symbol, SubType.K_5M)
            if ret == RET_OK:
                self.log("5分钟K线行情订阅成功", symbol=symbol)
            else:
                self.log(f"行情订阅失败: {data}", symbol=symbol)

        return True

    def get_market_data(self, symbol):
        """获取指定标的的实时市场数据"""
        ret, data = self.quote_context.get_cur_kline(symbol, num=300, ktype=SubType.K_5M)
        if ret == RET_OK:
            return data
        else:
            self.log(f"获取行情失败: {data}", symbol=symbol)
            return None

    def get_account_info(self):
        """获取账户信息"""
        ret, data = self.trade_context.accinfo_query(trd_env=TrdEnv.SIMULATE, acc_id=self.acc_id)
        if ret == RET_OK:
            return data
        else:
            self.log(f"获取账户信息失败: {data}")
            return None

    def place_order(self, symbol, price, qty, direction):
        """提交真实模拟订单"""
        if self.acc_id is None:
            self.log("未找到账户，无法下单", symbol=symbol)
            return

        trd_side = TrdSide.BUY if direction == 'BUY' else TrdSide.SELL
        ret, data = self.trade_context.place_order(
            price=price,
            qty=qty,
            code=symbol,
            trd_side=trd_side,
            order_type=OrderType.NORMAL,
            trd_env=TrdEnv.SIMULATE,
            acc_id=self.acc_id
        )

        if ret == RET_OK:
            self.log(f"{direction} 订单提交成功: {qty}{self.unit} @ {price}", symbol=symbol)
            self.log(f"订单详情: {data}", symbol=symbol)
            if direction == 'BUY':
                self.entry_prices[symbol] = price
        else:
            self.log(f"{direction} 订单提交失败: {data}", symbol=symbol)

    def simple_trading_strategy(self, symbol):
        """为指定标的执行交易策略"""
        market_data = self.get_market_data(symbol)
        if market_data is None or len(market_data) < 14:
            return

        ma13 = market_data['close'].rolling(window=13).mean()
        current_price = market_data['close'].iloc[-1]
        prev_price = market_data['close'].iloc[-2]
        current_ma13 = ma13.iloc[-1]
        prev_ma13 = ma13.iloc[-2]

        ret, position_data = self.trade_context.position_list_query(
            code=symbol, trd_env=TrdEnv.SIMULATE, acc_id=self.acc_id
        )
        current_position = 0
        if ret == RET_OK and not position_data.empty:
            current_position = int(position_data['qty'].iloc[0])

        if prev_price <= prev_ma13 and current_price > current_ma13 and current_position < self.max_position:
            self.place_order(symbol, current_price, 1, 'BUY')
        elif prev_price >= prev_ma13 and current_price < current_ma13 and current_position > 0:
            if symbol in self.entry_prices and current_price > self.entry_prices[symbol]:
                self.place_order(symbol, current_price, current_position, 'SELL')
                self.entry_prices.pop(symbol, None)

    def run(self):
        """运行模拟交易"""
        if not self.connect():
            return

        self.log("开始模拟交易...")
        try:
            while True:
                for symbol in self.symbols:
                    self.simple_trading_strategy(symbol)

                account_info = self.get_account_info()
                if account_info is not None:
                    self.log(f"当前资金: ${account_info['cash'].iloc[0]:.2f}")
                time.sleep(300)
        except KeyboardInterrupt:
            self.log("交易模拟结束")
            self.quote_context.close()
            self.trade_context.close()


if __name__ == "__main__":
    # 示例1：交易多个期货合约
    futures_symbols = ['HK.HSImain', 'HK.HTImain']
    futures_simulator = SingleCategoryTradingSimulator(trade_type='futures', symbols=futures_symbols)
    futures_simulator.run()

    # 示例2：交易多个股票
    stock_symbols = ['HK.00700', 'HK.01810', 'HK.09988']
    stock_simulator = SingleCategoryTradingSimulator(trade_type='stock', symbols=stock_symbols)
    stock_simulator.run()