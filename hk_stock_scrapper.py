import requests
from bs4 import BeautifulSoup
import pandas as pd
from datetime import datetime
import time


def get_index_constituents(index_type):
    """
    获取指定指数的成分股
    index_type: 'TECH' 为恒生科技指数，'HSI' 为恒生指数
    """

    # 设置请求头
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
    }

    try:
        # 构建URL
        if index_type == 'TECH':
            url = 'https://www.hsi.com.hk/chi/indexes/all-indexes/hstech'
        else:
            url = 'https://www.hsi.com.hk/chi/indexes/all-indexes/hsi'

        # 发送请求
        response = requests.get(url, headers=headers, verify=False)
        response.raise_for_status()

        # 使用BeautifulSoup解析HTML
        soup = BeautifulSoup(response.text, 'html.parser')

        # 找到成分股表格
        table = soup.find('table', {'class': 'table-style-01 tableSorter'})
        if not table:
            raise Exception("未找到成分股表格，网页结构可能已改变")

        # 提取表头
        headers = []
        for th in table.find('thead').find_all('th'):
            headers.append(th.text.strip())

        # 提取数据
        data = []
        for row in table.find('tbody').find_all('tr'):
            cols = row.find_all('td')
            if len(cols) >= 2:
                row_data = {}
                for i, col in enumerate(cols):
                    row_data[headers[i]] = col.text.strip()
                data.append(row_data)

        # 创建DataFrame并重命名列
        df = pd.DataFrame(data)
        column_mapping = {
            '股份編號': '股票代码',
            '股份名稱': '股票名称',
            '行業': '行业',
            '權重(%)*': '权重'
        }
        df = df.rename(columns=column_mapping)

        # 确保股票代码为4位数字
        df['股票代码'] = df['股票代码'].apply(lambda x: str(x).zfill(4))

        # 按股票代码排序
        df = df.sort_values('股票代码')

        return df

    except Exception as e:
        print(f"获取{'恒生科技' if index_type == 'TECH' else '恒生指数'}成分股数据时发生错误: {str(e)}")
        import traceback
        print(traceback.format_exc())
        return pd.DataFrame()


def main():
    # 获取当前时间作为文件名的一部分
    current_time = datetime.now().strftime('%Y%m%d_%H%M%S')

    # 添加 SSL 警告过滤
    import urllib3
    urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

    # 获取恒生科技指数成分股
    print('正在获取恒生科技指数成分股...')
    hstech_df = get_index_constituents('TECH')
    if not hstech_df.empty:
        hstech_filename = f'hstech_constituents_{current_time}.csv'
        hstech_df.to_csv(hstech_filename, index=False, encoding='utf-8-sig')
        print(f'恒生科技指数成分股已保存至: {hstech_filename}')
        print(f'共获取到 {len(hstech_df)} 只成分股\n')

    # 添加延时避免频繁请求
    time.sleep(2)

    # 获取恒生指数成分股
    print('正在获取恒生指数成分股...')
    hsi_df = get_index_constituents('HSI')
    if not hsi_df.empty:
        hsi_filename = f'hsi_constituents_{current_time}.csv'
        hsi_df.to_csv(hsi_filename, index=False, encoding='utf-8-sig')
        print(f'恒生指数成分股已保存至: {hsi_filename}')
        print(f'共获取到 {len(hsi_df)} 只成分股')


if __name__ == '__main__':
    main()