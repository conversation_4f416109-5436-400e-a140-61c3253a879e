from futu import *
import time
from datetime import datetime, time as datetime_time


class StockQuoteHandler(StockQuoteHandlerBase):
    def __init__(self):
        super(StockQuoteHandler, self).__init__()

    def on_recv_kline(self, kline_struct):
        """K线数据回调函数"""
        print(f"\n[{datetime.now()}] 收到K线数据更新:")
        print(f"股票: {kline_struct.code}")
        print(f"K线类型: {kline_struct.k_type}")

        if kline_struct.data_df is not None and not kline_struct.data_df.empty:
            latest_data = kline_struct.data_df.iloc[-1]
            print(f"最新价格: {latest_data['close']}")
            print(f"时间: {latest_data['time_key']}")

            # 调用策略函数
            self.run_strategy(kline_struct.data_df)

    def run_strategy(self, kline_df):
        """策略函数"""
        try:
            if len(kline_df) >= 20:
                ma20 = kline_df['close'].rolling(20).mean()
                current_price = kline_df['close'].iloc[-1]
                ma20_value = ma20.iloc[-1]

                print(f"当前价格: {current_price:.2f}")
                print(f"20日均线: {ma20_value:.2f}")

                if current_price > ma20_value:
                    print("信号: 价格在20日均线上方")
                else:
                    print("信号: 价格在20日均线下方")

        except Exception as e:
            print(f"策略执行出错: {str(e)}")


def is_trading_time():
    """判断是否为交易时间"""
    current_time = datetime.now().time()
    morning_start = datetime_time(9, 30)
    morning_end = datetime_time(12, 0)
    afternoon_start = datetime_time(13, 0)
    afternoon_end = datetime_time(16, 0)

    is_morning_session = morning_start <= current_time <= morning_end
    is_afternoon_session = afternoon_start <= current_time <= afternoon_end

    return is_morning_session or is_afternoon_session


def run_quote_connection():
    """建立行情连接并订阅K线"""
    quote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)

    # 设置回调处理对象
    handler = StockQuoteHandler()
    quote_ctx.set_handler(handler)

    # 订阅K线和逐笔数据
    stock_code = 'HK.00700'  # 腾讯控股

    # 先取消所有订阅
    quote_ctx.unsubscribe(stock_code, [SubType.K_1M, SubType.TICKER])

    # 重新订阅K线和逐笔数据
    ret_sub, err_message = quote_ctx.subscribe(
        code_list=[stock_code],
        subtype_list=[SubType.K_1M, SubType.TICKER],
        subscribe_push=True
    )

    if ret_sub != RET_OK:
        print(f'订阅失败: {err_message}')
        return None

    print(f'订阅成功: {stock_code}')

    # 获取当前K线数据
    ret, data = quote_ctx.get_cur_kline(
        code=stock_code,
        num=100,  # 获取最近100根K线
        ktype=SubType.K_1M,
        autype=AuType.QFQ  # 前复权
    )

    if ret == RET_OK:
        print('获取历史K线数据成功:')
        print(data.tail())  # 显示最新的几条数据
        handler.run_strategy(data)
    else:
        print(f'获取历史K线数据失败: {data}')

    return quote_ctx


def main():
    """主函数"""
    quote_ctx = None
    try:
        quote_ctx = run_quote_connection()
        if quote_ctx is None:
            print("行情连接创建失败")
            return

        print("开始监控市场...")
        while True:
            if is_trading_time():
                # 在交易时间内，定期更新数据状态
                print(f"\r等待K线更新... {datetime.now()}", end='')
                time.sleep(1)
            else:
                print(f"\n非交易时间 {datetime.now()}")
                time.sleep(60)

    except Exception as e:
        print(f"程序运行出错: {str(e)}")
    finally:
        if quote_ctx:
            quote_ctx.close()


if __name__ == "__main__":
    main()