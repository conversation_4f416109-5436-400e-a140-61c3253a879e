import numpy as np
import pandas as pd


def IFELSE(condition, true_value, false_value):
    """
    实现类似通达信IFELSE的条件判断函数
    """
    return np.where(condition, true_value, false_value)


def ISLASTBAR(df):
    """
    判断是否为最后一个Bar
    """
    return np.arange(len(df)) == len(df) - 1


def VALUEWHEN(condition, value):
    """
    获取满足条件时的值
    """
    result = np.full_like(value, np.nan)
    mask = condition
    result[mask] = value[mask]
    return result


def wavetrader(df):
    """
    Implements the WaveTrader strategy based on the given formula.

    Parameters:
    -----------
    df : pandas.DataFrame
        DataFrame with OHLC price data

    Returns:
    --------
    pandas.DataFrame
        DataFrame with original data and additional WaveTrader indicators
    """
    # Make a copy of the input DataFrame to avoid modifying the original
    result_df = df.copy()

    # Get required price data
    high = df['high'].values
    low = df['low'].values
    close = df['close'].values
    open_price = df['open'].values

    # Create shifted versions of high and low
    high_1 = np.roll(high, 1)
    high_2 = np.roll(high, 2)
    low_1 = np.roll(low, 1)
    low_2 = np.roll(low, 2)

    # Set first two elements to NaN since they don't have 2 previous values
    # Convert to float type
    high_1 = high_1.astype(float)
    high_2 = high_2.astype(float)
    low_1 = low_1.astype(float)
    low_2 = low_2.astype(float)
    # Now you can assign NaN
    high_1[:2] = np.nan
    high_2[:2] = np.nan
    low_1[:2] = np.nan
    low_2[:2] = np.nan

    # HH1: IFELSE(H<REF(H,1)&&REF(H,1)<REF(H,2),REF(H,2),0)
    # Detect two consecutive lower highs
    hh1_cond = (high < high_1) & (high_1 < high_2)
    hh1 = IFELSE(hh1_cond, high_2, 0)

    # LL1: IFELSE(L>REF(L,1)&&REF(L,1)>REF(L,2),REF(L,2),0)
    # Detect two consecutive higher lows
    ll1_cond = (low > low_1) & (low_1 > low_2)
    ll1 = IFELSE(ll1_cond, low_2, 0)

    # HH2: VALUEWHEN(HH1>0,HH1)
    # Remember the last significant high
    hh2 = VALUEWHEN(hh1 > 0, hh1)

    # LL2: VALUEWHEN(LL1>0,LL1)
    # Remember the last significant low
    ll2 = VALUEWHEN(ll1 > 0, ll1)

    # K1: IFELSE(CLOSE>HH2,-3,IFELSE(CLOSE<LL2,1,0))
    # Generate initial signal: -3 for buy (close above resistance), 1 for sell (close below support)
    k1 = IFELSE(close > hh2, -3, IFELSE(close < ll2, 1, 0))

    # K2: VALUEWHEN(K1<>0,K1),NODRAW
    # Remember the last non-zero signal
    k2 = VALUEWHEN(k1 != 0, k1)

    # G:=IFELSE(K2=1,HH2,LL2)
    # Store the reference price level (HH2 for sell signals, LL2 for buy signals)
    g = IFELSE(k2 == 1, hh2, ll2)

    # G1:=VALUEWHEN(ISLASTBAR,G)
    # Store the last value of G
    g1 = VALUEWHEN(ISLASTBAR(df), g)

    # W1:=K2
    # Store the signal
    w1 = k2

    # W2:=OPEN-CLOSE
    # Calculate the bar range
    w2 = open_price - close

    # HT:=IFELSE(OPEN>CLOSE,OPEN,CLOSE)
    # Higher of open or close
    ht = IFELSE(open_price > close, open_price, close)

    # LT:=IFELSE(OPEN<CLOSE,OPEN,CLOSE)
    # Lower of open or close
    lt = IFELSE(open_price < close, open_price, close)

    # Generate buy and sell signals
    buy_signals = (k2 == -3)
    sell_signals = (k2 == 1)

    # 组合信号序列
    signal_sequence = np.where(buy_signals, -1, np.where(sell_signals, 1, 0)).astype(float)
    signal_sequence = np.nan_to_num(signal_sequence, nan=0.0)
    # Add all calculated values to the result DataFrame
    result_df['HH1'] = hh1
    result_df['LL1'] = ll1
    result_df['HH2'] = hh2
    result_df['LL2'] = ll2
    result_df['K1'] = k1
    result_df['K2'] = k2
    result_df['G'] = g
    result_df['G1'] = g1
    result_df['W1'] = w1
    result_df['W2'] = w2
    result_df['HT'] = ht
    result_df['LT'] = lt
    result_df['BUY'] = buy_signals
    result_df['SELL'] = sell_signals
    result_df['SIGNAL_SEQUENCE'] = signal_sequence

    return result_df