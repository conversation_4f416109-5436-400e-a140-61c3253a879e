import pandas as pd
import numpy as np
import backtrader as bt
from datetime import datetime

class MultiOrderBreakoutStrategy(bt.Strategy):
    params = (
        ('risk_per_trade', 1.0),
        ('lookback', 20),
        ('breakout_mult', 2.0),
        ('stop_loss_percent', 2.0),
        ('max_positions', 5),
        ('atr_period', 14),
        ('ma_len', 100),
    )

    def __init__(self):
        self.dataclose = self.datas[0].close
        self.datahigh = self.datas[0].high
        self.datalow = self.datas[0].low

        # Indicators
        self.bb = bt.indicators.BollingerBands(self.dataclose, period=self.params.lookback,
                                               devfactor=self.params.breakout_mult)
        self.atr = bt.indicators.ATR(self.data, period=self.params.atr_period)
        self.sar = bt.indicators.ParabolicSAR(self.data)
        self.ma = bt.indicators.EMA(self.dataclose, period=self.params.ma_len)

        self.atr_sma = bt.indicators.SMA(self.atr, period=100)

        self.open_positions = 0

    def next(self):
        if len(self) < self.params.lookback:
            return

        # Entry conditions
        long_condition = (
                self.dataclose[0] > self.bb.top[0] and
                self.dataclose[0] > self.sar[0] and
                self.dataclose[0] > self.ma[0]
        )

        # Exit conditions
        exit_condition = (
                (self.dataclose[-1] > self.bb.mid[-1] and self.dataclose[0] <= self.bb.mid[0]) or
                (self.dataclose[-1] > self.sar[-1] and self.dataclose[0] <= self.sar[0])
        )

        # Dynamic position sizing
        position_size = (self.broker.getvalue() * self.params.risk_per_trade / 100) / (
                    self.dataclose[0] * self.params.stop_loss_percent / 100)

        # Strategy execution
        if long_condition and self.open_positions < self.params.max_positions and self.atr[0] > self.atr_sma[
            0] and position_size > 0:
            self.buy(size=position_size)
            self.open_positions += 1

            # Set stop loss
            stop_price = self.dataclose[0] * (1 - self.params.stop_loss_percent / 100)
            self.sell(exectype=bt.Order.Stop, price=stop_price, parent=self.order)

        # Close all positions on exit condition
        if exit_condition and self.open_positions > 0:
            self.close()
            self.open_positions = 0


def run_strategy():
    cerebro = bt.Cerebro()
    cerebro.addstrategy(MultiOrderBreakoutStrategy)

    # Add data feed
    data = bt.feeds.YahooFinanceData(dataname='AAPL', fromdate=datetime(2020, 1, 1), todate=datetime(2023, 1, 1))
    cerebro.adddata(data)

    # Set initial capital
    cerebro.broker.setcash(10000.0)

    # Set commission
    cerebro.broker.setcommission(commission=0.001)

    # Run the strategy
    cerebro.run()

    # Print final portfolio value
    print(f'Final Portfolio Value: ${cerebro.broker.getvalue():.2f}')

    # Plot the results
    cerebro.plot()


if __name__ == '__main__':
    run_strategy()
    