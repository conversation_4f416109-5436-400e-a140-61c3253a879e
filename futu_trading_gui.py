import tkinter as tk
from tkinter import ttk, messagebox
import math
from futu import *

def get_hk_stock_price(code):
    quote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)
    ret, data = quote_ctx.get_market_snapshot([code])
    quote_ctx.close()
    if ret == RET_OK and not data.empty:
        return data['last_price'].iloc[0]
    else:
        print('获取价格失败:', data)
        return None

def calc_futu_hk_trade_fee(price, qty):
    amount = price * qty
    commission = max(amount * 0.0003, 15)  # 佣金：0.03%，最低15港元
    platform_fee = 15                     # 平台使用费：15港元/笔
    stamp_duty = max(int(amount * 0.0013 + 0.9999), 1)  # 印花税：0.13%，最少1港元，向上取整
    trading_fee = round(amount * 0.00005, 3)            # 交易费：0.005%
    trading_sys_fee = round(amount * 0.00003, 3)        # 交易系统使用费：0.003%
    transaction_levy = round(amount * 0.00002, 3)       # 经手费：0.002%
    total_fee = commission + platform_fee + stamp_duty + trading_fee + trading_sys_fee + transaction_levy
    return {
        '成交金额': round(amount, 3),
        '佣金': round(commission, 3),
        '平台使用费': platform_fee,
        '印花税': stamp_duty,
        '交易费': trading_fee,
        '交易系统使用费': trading_sys_fee,
        '经手费': transaction_levy,
        '总费用': round(total_fee, 3)
    }

def calc_min_sell_price(buy_price, qty, target_profit_rate=0.003):
    fee_info = calc_futu_hk_trade_fee(buy_price, qty)
    buy_cost = buy_price * qty + fee_info['总费用']
    left = buy_price
    right = buy_price * 2
    for _ in range(100):
        mid = (left + right) / 2
        sell_fee_info = calc_futu_hk_trade_fee(mid, qty)
        sell_income = mid * qty - sell_fee_info['总费用']
        profit_rate = (sell_income - buy_cost) / buy_cost
        if profit_rate < target_profit_rate:
            left = mid
        else:
            right = mid
    return round(right, 3)

class FutuTradingApp:
    def __init__(self, root):
        self.root = root
        self.root.title("富途交易获利计算器")
        self.root.geometry("600x500")
        self.root.resizable(False, False)
        
        # 创建样式
        style = ttk.Style()
        style.configure("TLabel", font=("微软雅黑", 10))
        style.configure("TButton", font=("微软雅黑", 10))
        style.configure("TEntry", font=("微软雅黑", 10))
        
        # 创建主框架
        main_frame = ttk.Frame(root, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 输入区域
        input_frame = ttk.LabelFrame(main_frame, text="输入参数", padding="10")
        input_frame.pack(fill=tk.X, pady=10)
        
        # 股票代码
        ttk.Label(input_frame, text="港股代码 (如07226):").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.code_var = tk.StringVar()
        ttk.Entry(input_frame, textvariable=self.code_var, width=15).grid(row=0, column=1, sticky=tk.W, pady=5)
        ttk.Button(input_frame, text="获取当前价格", command=self.get_current_price).grid(row=0, column=2, padx=10, pady=5)
        
        # 买入价格
        ttk.Label(input_frame, text="买入价格 (港元):").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.price_var = tk.StringVar()
        ttk.Entry(input_frame, textvariable=self.price_var, width=15).grid(row=1, column=1, sticky=tk.W, pady=5)
        
        # 买入数量
        ttk.Label(input_frame, text="买入数量:").grid(row=2, column=0, sticky=tk.W, pady=5)
        self.qty_var = tk.StringVar()
        ttk.Entry(input_frame, textvariable=self.qty_var, width=15).grid(row=2, column=1, sticky=tk.W, pady=5)
        
        # 目标获利率
        ttk.Label(input_frame, text="目标获利率 (%):").grid(row=3, column=0, sticky=tk.W, pady=5)
        self.profit_rate_var = tk.StringVar(value="0.3")
        ttk.Entry(input_frame, textvariable=self.profit_rate_var, width=15).grid(row=3, column=1, sticky=tk.W, pady=5)
        
        # 计算按钮
        ttk.Button(input_frame, text="计算", command=self.calculate).grid(row=4, column=0, columnspan=3, pady=10)
        
        # 结果区域
        self.result_frame = ttk.LabelFrame(main_frame, text="计算结果", padding="10")
        self.result_frame.pack(fill=tk.BOTH, expand=True, pady=10)
        
        # 结果文本框
        self.result_text = tk.Text(self.result_frame, height=15, width=60, font=("微软雅黑", 10))
        self.result_text.pack(fill=tk.BOTH, expand=True)
        
    def get_current_price(self):
        code_input = self.code_var.get().strip()
        if not code_input:
            messagebox.showerror("错误", "请输入港股代码")
            return
            
        code = f'HK.{code_input.zfill(5)}'
        price = get_hk_stock_price(code)
        if price is not None:
            self.price_var.set(f"{price:.3f}")
        else:
            messagebox.showerror("错误", "获取价格失败，请检查代码是否正确")
    
    def calculate(self):
        try:
            code_input = self.code_var.get().strip()
            price = float(self.price_var.get())
            qty = int(self.qty_var.get())
            profit_rate = float(self.profit_rate_var.get()) / 100  # 转换为小数
            
            if not code_input or price <= 0 or qty <= 0 or profit_rate <= 0:
                messagebox.showerror("错误", "请输入有效的参数")
                return
                
            code = f'HK.{code_input.zfill(5)}'
            fee_info = calc_futu_hk_trade_fee(price, qty)
            min_sell_price = calc_min_sell_price(price, qty, profit_rate)
            
            # 清空结果文本框
            self.result_text.delete(1.0, tk.END)
            
            # 显示结果
            self.result_text.insert(tk.END, f"股票代码: {code}\n")
            self.result_text.insert(tk.END, f"当前价格: {price:.3f} 港元\n")
            self.result_text.insert(tk.END, f"买入数量: {qty} 股\n\n")
            
            self.result_text.insert(tk.END, "买入费用明细:\n")
            for k, v in fee_info.items():
                self.result_text.insert(tk.END, f"{k}: {v:.3f} 港元\n")
            
            self.result_text.insert(tk.END, f"\n若要净赚 {profit_rate*100:.2f}%, 最低卖出价需达到: {min_sell_price:.3f} 港元\n")
            price_diff = min_sell_price - price
            price_diff_percent = (price_diff / price) * 100
            self.result_text.insert(tk.END, f"相比买入价上涨: {price_diff:.3f} 港元 ({price_diff_percent:.2f}%)\n")
            
        except ValueError:
            messagebox.showerror("错误", "请输入有效的数字")
        except Exception as e:
            messagebox.showerror("错误", f"计算过程中出错: {str(e)}")

if __name__ == '__main__':
    root = tk.Tk()
    app = FutuTradingApp(root)
    root.mainloop()