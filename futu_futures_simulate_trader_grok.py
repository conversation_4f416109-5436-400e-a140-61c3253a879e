import futu as ft
from futu import *
import time
import pandas as pd
from datetime import datetime


class FuturesTradingSimulator:
    def __init__(self, host='127.0.0.1', port=11111):
        # 初始化Futu API连接
        self.trade_context = OpenUSTradeContext(host=host, port=port)  # 交易上下文
        self.quote_context = OpenQuoteContext(host=host, port=port)  # 行情上下文

        # 交易参数
        self.symbol = "HK.HSImain"  # 恒生指数期货主力合约
        self.lot_size = 50  # 每手合约单位

    def connect(self):
        """建立连接并订阅行情"""
        # 无需解锁，直接检查连接
        ret, data = self.trade_context.get_acc_list()
        if ret == RET_OK:
            print('交易接口连接成功:', data)
        else:
            print('交易接口连接失败:', data)
            return False

        # 订阅行情
        ret, data = self.quote_context.subscribe(self.symbol, SubType.K_1M)
        if ret == RET_OK:
            print('行情订阅成功')
        else:
            print('行情订阅失败:', data)
            return False
        return True

    def get_market_data(self):
        """获取实时市场数据"""
        ret, data = self.quote_context.get_cur_kline(self.symbol, num=5, ktype=SubType.K_1M)
        if ret == RET_OK:
            return data
        else:
            print('获取行情失败:', data)
            return None

    def get_account_info(self):
        """获取账户信息（指定模拟环境）"""
        ret, data = self.trade_context.accinfo_query(trd_env=TrdEnv.SIMULATE)
        if ret == RET_OK:
            return data
        else:
            print('获取账户信息失败:', data)
            return None

    def calculate_position_size(self, price):
        """计算仓位大小"""
        account_info = self.get_account_info()
        if account_info is None:
            return 1
        cash = float(account_info['cash'].iloc[0])  # 获取可用资金
        risk_per_trade = cash * 0.01  # 每笔交易风险1%
        position_size = int(risk_per_trade / (price * self.lot_size))
        return max(1, position_size)  # 至少交易1手

    def place_order(self, price, qty, direction):
        """提交真实模拟订单"""
        if direction == 'BUY':
            trd_side = TrdSide.BUY
        elif direction == 'SELL':
            trd_side = TrdSide.SELL
        else:
            print("无效的交易方向")
            return

        # 提交订单（指定模拟环境）
        ret, data = self.trade_context.place_order(
            price=price,
            qty=qty,
            code=self.symbol,
            trd_side=trd_side,
            order_type=OrderType.NORMAL,  # 普通限价单
            trd_env=TrdEnv.SIMULATE  # 指定模拟交易环境
        )

        if ret == RET_OK:
            print(f"{direction} 订单提交成功: {qty}手 @ {price}")
            print("订单详情:", data)
        else:
            print(f"{direction} 订单提交失败:", data)

    def simple_trading_strategy(self):
        """简单交易策略：基于5周期均线的趋势跟随"""
        market_data = self.get_market_data()
        if market_data is None or len(market_data) < 5:
            return

        # 计算5周期简单移动平均线
        ma5 = market_data['close'].rolling(window=5).mean().iloc[-1]
        current_price = market_data['close'].iloc[-1]

        # 获取当前持仓（指定模拟环境）
        ret, position_data = self.trade_context.position_list_query(code=self.symbol, trd_env=TrdEnv.SIMULATE)
        current_position = 0
        if ret == RET_OK and not position_data.empty:
            current_position = int(position_data['qty'].iloc[0])

        position_size = self.calculate_position_size(current_price)

        # 交易逻辑
        if current_price > ma5 and current_position == 0:
            self.place_order(current_price, position_size, 'BUY')
        elif current_price < ma5 and current_position > 0:
            self.place_order(current_price, min(position_size, current_position), 'SELL')

    def run(self):
        """运行模拟交易"""
        if not self.connect():
            return

        print("开始模拟交易...")
        try:
            while True:
                self.simple_trading_strategy()
                account_info = self.get_account_info()
                if account_info is not None:
                    print(f"当前资金: ${account_info['cash'].iloc[0]:.2f}")
                time.sleep(60)  # 每分钟检查一次
        except KeyboardInterrupt:
            print("\n交易模拟结束")
            self.quote_context.close()
            self.trade_context.close()


if __name__ == "__main__":
    simulator = FuturesTradingSimulator()
    simulator.run()