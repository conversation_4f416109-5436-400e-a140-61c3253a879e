from futu import *
import pandas as pd
import time


def get_hk_futures_positions_auto():
    # 连接FutuOpenD
    quote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)  # 行情上下文
    trade_ctx = OpenFutureTradeContext(host='127.0.0.1', port=11111)  # 期货交易上下文

    try:
        # 第一步：获取所有交易账户
        ret, acc_list = trade_ctx.get_acc_list()
        if ret != RET_OK:
            print(f"获取账户列表失败: {acc_list}")
            return

        print("可用账户列表:")
        print(acc_list)

        # 筛选出模拟账户
        mock_acc_list = [acc for acc in acc_list.to_dict('records') if acc.get('trd_env') == TrdEnv.SIMULATE]

        if not mock_acc_list:
            print("没有找到模拟账户，请先在富途牛牛APP中开通模拟账户")
            return

        print(f"\n找到 {len(mock_acc_list)} 个模拟账户:")
        for idx, acc in enumerate(mock_acc_list):
            print(f"{idx + 1}. {acc.get('acc_id')} - {acc.get('trd_market_str')} - {acc.get('acc_type_str')}")

        # 选择香港期货模拟账户（如果有）
        hk_future_acc = None
        for acc in mock_acc_list:
            if acc.get('trd_market') == TrdMarket.HK_FUTURE:
                hk_future_acc = acc
                break

        # 如果没有香港期货模拟账户，选择第一个模拟账户
        if not hk_future_acc and mock_acc_list:
            hk_future_acc = mock_acc_list[0]
            print(f"\n未找到香港期货模拟账户，使用第一个可用的模拟账户: {hk_future_acc.get('acc_id')}")
        elif hk_future_acc:
            print(f"\n使用香港期货模拟账户: {hk_future_acc.get('acc_id')}")
        else:
            print("没有找到可用的模拟账户")
            return

        # 设置为选定的模拟账户
        trade_ctx.set_acc_id(acc_id=hk_future_acc.get('acc_id'))

        # 确认已切换到正确的账户
        ret, data = trade_ctx.accinfo_query()
        if ret != RET_OK:
            print(f"获取账户信息失败: {data}")
            return

        print("\n当前账户信息:")
        print(data)

        # 获取持仓信息
        ret, position_data = trade_ctx.position_list_query()
        if ret != RET_OK:
            print(f"获取持仓信息失败: {position_data}")
            return

        if position_data.empty:
            print("\n当前没有持仓")
        else:
            print("\n当前期货持仓信息:")
            # 格式化输出，只显示关键字段
            try:
                selected_columns = [
                    'code', 'position_side', 'qty', 'can_sell_qty',
                    'nominal_price', 'cost_price', 'position_value',
                    'pl_val', 'pl_ratio'
                ]

                # 检查所有列是否都存在
                available_columns = [col for col in selected_columns if col in position_data.columns]

                if available_columns:
                    formatted_data = position_data[available_columns]
                    print(formatted_data)
                else:
                    print(position_data)  # 如果列名不匹配，则打印全部数据
            except Exception as e:
                print(f"处理数据时出错: {e}")
                print(position_data)  # 出错时打印原始数据

        return position_data

    except Exception as e:
        print(f"发生异常: {e}")
        import traceback
        print(traceback.format_exc())

    finally:
        # 关闭连接
        trade_ctx.close()
        quote_ctx.close()


def main():
    print("正在连接富途API并获取模拟账户信息...")

    # 尝试连接，如果失败则重试
    max_retries = 3
    for i in range(max_retries):
        try:
            position_data = get_hk_futures_positions_auto()

            # 如果需要将数据保存到CSV
            if position_data is not None and not position_data.empty:
                filename = f"hk_futures_positions_{time.strftime('%Y%m%d_%H%M%S')}.csv"
                position_data.to_csv(filename, index=False)
                print(f"已将持仓数据保存至 {filename}")

            break  # 成功获取数据，跳出循环

        except Exception as e:
            print(f"第 {i + 1} 次尝试失败: {e}")
            if i < max_retries - 1:
                print(f"等待 5 秒后重试...")
                time.sleep(5)
            else:
                print("已达到最大重试次数，程序退出")


if __name__ == "__main__":
    # 运行前确保FutuOpenD已经启动，并且已登录
    main()